# Django Settings
SECRET_KEY=your-super-secret-key-here-change-this-in-production
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# Database Configuration
# PostgreSQL (recommended for production)
DATABASE_URL=postgresql://username:password@localhost:5432/saverapi
# SQLite (for development only)
# DATABASE_URL=sqlite:///db.sqlite3

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Telegram API Configuration
# Get these from https://my.telegram.org/apps
API_ID=your-telegram-api-id
API_HASH=your-telegram-api-hash

# Authentication
# Generate a strong random token for API authentication
X_AUTH_TOKEN=your-api-authentication-token-here

# Logging Configuration
LOG_LEVEL=INFO

# Email Configuration (Optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
DEFAULT_FROM_EMAIL=<EMAIL>

# Monitoring (Optional)
# Sentry DSN for error tracking
SENTRY_DSN=https://your-sentry-dsn-here

# Cache Configuration
CACHE_TTL=3600

# Rate Limiting
RATE_LIMIT_PER_MINUTE=100
RATE_LIMIT_BURST=10

# Worker Configuration
MAX_CONCURRENT_DOWNLOADS=5
TASK_TIMEOUT=300
RETRY_ATTEMPTS=3

# Security Settings (Production)
SECURE_SSL_REDIRECT=False
SECURE_HSTS_SECONDS=0
SECURE_HSTS_INCLUDE_SUBDOMAINS=False
SECURE_HSTS_PRELOAD=False

# CORS Settings
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# File Storage (Optional)
# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_STORAGE_BUCKET_NAME=your-s3-bucket-name
AWS_S3_REGION_NAME=us-east-1

# Media Settings
MAX_FILE_SIZE=100MB
ALLOWED_FILE_TYPES=mp4,jpg,jpeg,png,gif,webp

# Bot Configuration
BOT_REQUEST_TIMEOUT=30
BOT_MAX_RETRIES=3
BOT_COOLDOWN_MINUTES=5
MAX_TASKS_PER_COOLDOWN=2

# Queue Configuration
QUEUE_CHECK_INTERVAL=15
QUEUE_MAX_SIZE=1000

# Development Settings
DJANGO_SETTINGS_MODULE=backend.settings
