# SaverAPI - Social Media Downloader Service

A Django-based REST API service that enables downloading media content from social media platforms (Instagram, TikTok, YouTube) using Telegram bots as intermediaries.

## 🚀 Features

- **Multi-Platform Support**: Download content from Instagram, TikTok, and YouTube
- **Telegram Bot Integration**: Uses Telegram bots as download intermediaries
- **Asynchronous Processing**: Redis-based task queue for handling download requests
- **User Bot Management**: Pyrogram-based user bots for automated media forwarding
- **Task Tracking**: Real-time status tracking for download tasks
- **Authentication**: X-AUTH header-based API authentication
- **Scalable Architecture**: Support for multiple user accounts and target bots

## 📋 Table of Contents

- [Architecture Overview](#architecture-overview)
- [Installation](#installation)
- [Configuration](#configuration)
- [API Documentation](#api-documentation)
- [Bot Management](#bot-management)
- [Usage Examples](#usage-examples)
- [Development](#development)
- [Deployment](#deployment)

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client App    │───▶│   Django API    │───▶│   Redis Queue   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   PostgreSQL    │    │   User Bots     │
                       │   Database      │    │  (Pyrogram)     │
                       └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
                                              ┌─────────────────┐
                                              │  Target Bots    │
                                              │  (Telegram)     │
                                              └─────────────────┘
```

### Core Components

1. **Django REST API**: Main service handling download requests
2. **Redis Queue**: Asynchronous task processing
3. **PostgreSQL**: Data persistence for tasks and bot configurations
4. **Pyrogram User Bots**: Automated Telegram clients for media forwarding
5. **Target Bots**: Telegram bots that perform actual media downloads

## 🛠️ Installation

### Prerequisites

- Python 3.10+
- PostgreSQL 12+
- Redis 6+
- Telegram API credentials (API_ID, API_HASH)

### Setup Steps

1. **Clone the repository**
```bash
git clone <repository-url>
cd saverapi
```

2. **Create virtual environment**
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install dependencies**
```bash
pip install -r requirements.txt
```

4. **Environment Configuration**
```bash
cp .env.example .env
# Edit .env with your configuration
```

5. **Database Setup**
```bash
python manage.py migrate
python manage.py createsuperuser
```

6. **Run the development server**
```bash
python manage.py runserver
```

## ⚙️ Configuration

### Environment Variables

Create a `.env` file in the project root:

```env
# Django Settings
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/saverapi

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Telegram API
API_ID=your-telegram-api-id
API_HASH=your-telegram-api-hash

# Authentication
X_AUTH_TOKEN=your-api-auth-token

# Logging
LOG_LEVEL=INFO
```

### Django Settings

Key settings in `backend/settings.py`:

- **INSTALLED_APPS**: Includes `saver`, `bots`, `userbots` apps
- **MIDDLEWARE**: Custom X-AUTH authentication middleware
- **DATABASES**: PostgreSQL configuration
- **CACHES**: Redis cache configuration

## 📚 API Documentation

### Base URL
```
http://localhost:8000/v1/
```

### Authentication
All API requests require the `X-AUTH` header:
```
X-AUTH: your-auth-token
```

### Endpoints

#### 1. Create Download Task

**POST** `/v1/media-dl/`

Creates a new media download task.

**Request Body:**
```json
{
    "url": "https://www.instagram.com/p/example/",
    "hosting": "instagram",
    "bot_token": "*********0:ABCdefGHIjklMNOpqrsTUVwxyz",
    "chat_id": "*********",
    "bot_username": "@your_bot"  // Optional, auto-fetched if not provided
}
```

**Response:**
```json
{
    "task_id": "550e8400-e29b-41d4-a716-************",
    "status": "pending",
    "message": "Task created successfully"
}
```

#### 2. Check Task Status

**GET** `/v1/check/{task_id}/status`

Retrieves the current status of a download task.

**Response:**
```json
{
    "task_id": "550e8400-e29b-41d4-a716-************",
    "status": "completed",
    "url": "https://www.instagram.com/p/example/",
    "hosting": "instagram",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:32:00Z",
    "account_name": "user_account_1",
    "message_id": 12345,
    "file_id": "BAADBAADrwADBREAAWdXAAE...",
    "error_message": null
}
```

### Status Values

- `pending`: Task created, waiting to be processed
- `processing`: Task is being processed by a user bot
- `completed`: Media successfully downloaded and forwarded
- `failed`: Task failed with error

## 🤖 Bot Management

### Target Bots

Target bots are Telegram bots that perform the actual media downloads. Manage them through Django admin or programmatically.

**Model: `TargetBots`**
```python
class TargetBots(models.Model):
    name = models.CharField(max_length=100)
    username = models.CharField(max_length=100)
    is_active = models.BooleanField(default=False)
    can_dl_from_insta = models.BooleanField(default=False)
    can_dl_from_tiktok = models.BooleanField(default=False)
    can_dl_from_youtube = models.BooleanField(default=False)
```

### User Bots (Pyrogram Clients)

User bots are Telegram user accounts that forward download requests to target bots and relay responses back.

#### Adding a User Bot

```bash
python manage.py add_user_bot
```

Follow the interactive prompts to authenticate a Telegram account.

#### Running User Bots

```bash
python manage.py run_user_bots
```

This starts all configured user bots to process the download queue.

### Bot Utilities

The `TelegramBotUtils` class provides helper methods:

```python
from bots.utils import TelegramBotUtils

# Get bot information
bot_info = TelegramBotUtils.get_bot_info(bot_token)

# Get bot username
username = TelegramBotUtils.get_bot_username(bot_token)

# Validate bot token
is_valid = TelegramBotUtils.validate_bot_token(bot_token)
```

## 💡 Usage Examples

### Python Client Example

```python
import requests

# Configuration
API_BASE_URL = "http://localhost:8000/v1"
AUTH_TOKEN = "your-auth-token"

headers = {
    "X-AUTH": AUTH_TOKEN,
    "Content-Type": "application/json"
}

# Create download task
download_data = {
    "url": "https://www.instagram.com/p/example/",
    "hosting": "instagram",
    "bot_token": "*********0:ABCdefGHIjklMNOpqrsTUVwxyz",
    "chat_id": "*********"
}

response = requests.post(
    f"{API_BASE_URL}/media-dl/",
    json=download_data,
    headers=headers
)

task_info = response.json()
task_id = task_info["task_id"]

# Check task status
status_response = requests.get(
    f"{API_BASE_URL}/check/{task_id}/status",
    headers=headers
)

status_info = status_response.json()
print(f"Task Status: {status_info['status']}")
```

### cURL Examples

**Create Download Task:**
```bash
curl -X POST http://localhost:8000/v1/media-dl/ \
  -H "X-AUTH: your-auth-token" \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://www.instagram.com/p/example/",
    "hosting": "instagram",
    "bot_token": "*********0:ABCdefGHIjklMNOpqrsTUVwxyz",
    "chat_id": "*********"
  }'
```

**Check Task Status:**
```bash
curl -X GET http://localhost:8000/v1/check/550e8400-e29b-41d4-a716-************/status \
  -H "X-AUTH: your-auth-token"
```

## 🔧 Development

### Project Structure

```
saverapi/
├── backend/                 # Django project settings
│   ├── __init__.py
│   ├── settings.py         # Main configuration
│   ├── urls.py            # URL routing
│   ├── middleware.py      # Custom middleware
│   └── wsgi.py           # WSGI configuration
├── saver/                  # Main API app
│   ├── models.py          # DownloadTask model
│   ├── views.py           # API endpoints
│   ├── serializers.py     # Request/response serializers
│   └── urls.py           # App URL patterns
├── bots/                   # Bot management app
│   ├── models.py          # TargetBots model
│   ├── service.py         # Bot service layer
│   └── utils.py          # Telegram utilities
├── userbots/              # User bot management
│   └── management/
│       └── commands/
│           ├── add_user_bot.py    # Add new user bot
│           └── run_user_bots.py   # Run bot processors
├── requirements.txt       # Python dependencies
└── manage.py             # Django management script
```

### Running Tests

```bash
# Run all tests
python manage.py test

# Run specific app tests
python manage.py test saver
python manage.py test bots

# Run with coverage
pip install coverage
coverage run --source='.' manage.py test
coverage report
```

### Code Quality

```bash
# Install development dependencies
pip install black flake8 isort

# Format code
black .
isort .

# Lint code
flake8 .
```

### Database Migrations

```bash
# Create migrations
python manage.py makemigrations

# Apply migrations
python manage.py migrate

# Show migration status
python manage.py showmigrations
```

## 🚀 Deployment

### Production Settings

Create a production settings file `backend/settings_prod.py`:

```python
from .settings import *

DEBUG = False
ALLOWED_HOSTS = ['your-domain.com', 'api.your-domain.com']

# Security settings
SECURE_SSL_REDIRECT = True
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True

# Database
DATABASES = {
    'default': dj_database_url.parse(os.environ.get('DATABASE_URL'))
}

# Static files
STATIC_ROOT = '/var/www/static/'
```

### Docker Deployment

**Dockerfile:**
```dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["gunicorn", "--bind", "0.0.0.0:8000", "backend.wsgi:application"]
```

**docker-compose.yml:**
```yaml
version: '3.8'

services:
  web:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=******************************/saverapi
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis

  db:
    image: postgres:13
    environment:
      POSTGRES_DB: saverapi
      POSTGRES_USER: user
      POSTGRES_PASSWORD: pass
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:6-alpine

  worker:
    build: .
    command: python manage.py run_user_bots
    depends_on:
      - db
      - redis

volumes:
  postgres_data:
```

### Systemd Service

Create `/etc/systemd/system/saverapi.service`:

```ini
[Unit]
Description=SaverAPI Django Application
After=network.target

[Service]
Type=notify
User=www-data
Group=www-data
WorkingDirectory=/var/www/saverapi
Environment=DJANGO_SETTINGS_MODULE=backend.settings_prod
ExecStart=/var/www/saverapi/venv/bin/gunicorn --bind unix:/run/saverapi.sock backend.wsgi:application
ExecReload=/bin/kill -s HUP $MAINPID
Restart=on-failure

[Install]
WantedBy=multi-user.target
```

### Nginx Configuration

```nginx
server {
    listen 80;
    server_name api.your-domain.com;

    location / {
        proxy_pass http://unix:/run/saverapi.sock;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static/ {
        alias /var/www/static/;
    }
}
```

## 📊 Monitoring and Logging

### Logging Configuration

```python
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': '/var/log/saverapi/django.log',
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['file'],
        'level': 'INFO',
    },
}
```

### Health Check Endpoint

Add to `backend/urls.py`:

```python
from django.http import JsonResponse

def health_check(request):
    return JsonResponse({'status': 'healthy', 'timestamp': timezone.now()})

urlpatterns = [
    path('health/', health_check),
    # ... other patterns
]
```

## 🔒 Security Considerations

1. **API Authentication**: Always use strong X-AUTH tokens
2. **Bot Token Security**: Store bot tokens securely, never log them
3. **Rate Limiting**: Implement rate limiting for API endpoints
4. **Input Validation**: Validate all user inputs and URLs
5. **HTTPS**: Use HTTPS in production
6. **Database Security**: Use strong database passwords and restrict access

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:

- Create an issue on GitHub
- Check the documentation
- Review the API examples

## 🔄 Changelog

### v1.0.0
- Initial release
- Instagram, TikTok, YouTube support
- Telegram bot integration
- Asynchronous task processing
- User bot management
