#!/usr/bin/env python
"""
Test script for the cooldown system implementation.
Run this to verify that the cooldown system works correctly.
"""

import os
import sys
import django
import asyncio
from datetime import datetime

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from bots.models import TargetBots, BotTargetCooldown
from bots.cooldown_service import CooldownService
from asgiref.sync import sync_to_async


async def test_cooldown_system():
    """Test the cooldown system functionality"""
    print("🧪 Testing Cooldown System")
    print("=" * 50)
    
    # Test data
    user_bot_name = "test_user_bot"
    
    # Get or create a test target bot
    target_bot, created = await sync_to_async(TargetBots.objects.get_or_create)(
        username="test_target_bot",
        defaults={
            'name': 'Test Target Bot',
            'is_active': True,
            'can_dl_from_insta': True,
            'can_dl_from_tiktok': True,
            'can_dl_from_youtube': True
        }
    )
    
    if created:
        print(f"✅ Created test target bot: @{target_bot.username}")
    else:
        print(f"ℹ️ Using existing target bot: @{target_bot.username}")
    
    # Clear any existing cooldown for this test
    await sync_to_async(BotTargetCooldown.objects.filter(
        user_bot_name=user_bot_name,
        target_bot=target_bot
    ).delete)()
    
    print(f"\n🔍 Testing with user bot: {user_bot_name}")
    print(f"🔍 Testing with target bot: @{target_bot.username}")
    
    # Test 1: Initial availability check
    print("\n1️⃣ Testing initial availability...")
    is_available = await CooldownService.is_bot_available(user_bot_name, target_bot)
    print(f"   Result: {'✅ Available' if is_available else '❌ Not available'}")
    assert is_available, "Bot should be available initially"
    
    # Test 2: Mark bot as used
    print("\n2️⃣ Marking bot as used...")
    await CooldownService.mark_bot_used(user_bot_name, target_bot)
    print("   ✅ Marked as used")
    
    # Test 3: Check availability after use
    print("\n3️⃣ Checking availability after use...")
    is_available = await CooldownService.is_bot_available(user_bot_name, target_bot)
    print(f"   Result: {'✅ Available' if is_available else '❌ Not available (in cooldown)'}")
    assert not is_available, "Bot should be in cooldown after use"
    
    # Test 4: Get cooldown status
    print("\n4️⃣ Getting cooldown status...")
    cooldown_status = await CooldownService.get_cooldown_status(user_bot_name)
    target_status = cooldown_status.get(target_bot.username, {})
    
    if target_status:
        available = target_status.get('available', True)
        remaining = target_status.get('remaining_seconds', 0)
        print(f"   Available: {available}")
        print(f"   Remaining time: {remaining // 60}m {remaining % 60}s")
        assert not available, "Status should show bot as not available"
        assert remaining > 0, "Should have remaining cooldown time"
    else:
        print("   ❌ No status found for target bot")
        assert False, "Should have cooldown status"
    
    # Test 5: Get available target bots
    print("\n5️⃣ Getting available target bots for Instagram...")
    available_bots = await CooldownService._get_available_target_bots(user_bot_name, "instagram")
    available_usernames = [bot.username for bot in available_bots]
    print(f"   Available bots: {available_usernames}")
    
    # Our test bot should not be in the available list
    assert target_bot.username not in available_usernames, "Test bot should not be available due to cooldown"
    
    # Test 6: Get next available user bot
    print("\n6️⃣ Testing user bot selection...")
    combination = await CooldownService.get_next_available_user_bot([user_bot_name], "instagram")
    if combination:
        selected_user, selected_target = combination
        print(f"   Selected combination: {selected_user} -> @{selected_target.username}")
        assert selected_target.username != target_bot.username, "Should not select the bot in cooldown"
    else:
        print("   ℹ️ No available combinations (expected if only test bot exists)")
    
    # Test 7: Test cleanup function
    print("\n7️⃣ Testing cleanup function...")
    deleted_count = await CooldownService.cleanup_old_cooldowns(days_old=0)  # Delete all records
    print(f"   Cleaned up {deleted_count} old records")
    
    # Test 8: Verify cleanup worked
    print("\n8️⃣ Verifying cleanup...")
    is_available_after_cleanup = await CooldownService.is_bot_available(user_bot_name, target_bot)
    print(f"   Available after cleanup: {'✅ Yes' if is_available_after_cleanup else '❌ No'}")
    assert is_available_after_cleanup, "Bot should be available after cleanup"
    
    print("\n🎉 All tests passed!")
    print("=" * 50)
    
    # Cleanup test data
    if created:
        await sync_to_async(target_bot.delete)()
        print("🧹 Cleaned up test target bot")


async def test_multiple_bots():
    """Test with multiple user bots and target bots"""
    print("\n🧪 Testing Multiple Bots Scenario")
    print("=" * 50)
    
    # Create test target bots
    test_bots = []
    for i in range(3):
        bot, created = await sync_to_async(TargetBots.objects.get_or_create)(
            username=f"test_bot_{i}",
            defaults={
                'name': f'Test Bot {i}',
                'is_active': True,
                'can_dl_from_insta': True,
                'can_dl_from_tiktok': False,
                'can_dl_from_youtube': False
            }
        )
        test_bots.append(bot)
        if created:
            print(f"✅ Created test bot: @{bot.username}")
    
    user_bots = ["user_bot_1", "user_bot_2"]
    
    # Clear existing cooldowns
    await sync_to_async(BotTargetCooldown.objects.filter(user_bot_name__in=user_bots).delete)()
    
    print(f"\n🔍 Testing with user bots: {user_bots}")
    print(f"🔍 Testing with target bots: {[f'@{bot.username}' for bot in test_bots]}")
    
    # Test getting all available combinations
    print("\n1️⃣ Getting all available combinations...")
    combinations = await CooldownService.get_all_available_combinations(user_bots, "instagram")
    print(f"   Found {len(combinations)} combinations:")
    for user_bot, target_bot in combinations:
        print(f"     {user_bot} -> @{target_bot.username}")
    
    expected_combinations = len(user_bots) * len(test_bots)
    assert len(combinations) == expected_combinations, f"Should have {expected_combinations} combinations"
    
    # Test using some combinations
    print("\n2️⃣ Using some combinations...")
    for i, (user_bot, target_bot) in enumerate(combinations[:2]):  # Use first 2 combinations
        await CooldownService.mark_bot_used(user_bot, target_bot)
        print(f"   Used: {user_bot} -> @{target_bot.username}")
    
    # Check available combinations after use
    print("\n3️⃣ Checking available combinations after use...")
    remaining_combinations = await CooldownService.get_all_available_combinations(user_bots, "instagram")
    print(f"   Remaining: {len(remaining_combinations)} combinations")
    
    expected_remaining = expected_combinations - 2
    assert len(remaining_combinations) == expected_remaining, f"Should have {expected_remaining} combinations remaining"
    
    print("\n🎉 Multiple bots test passed!")
    
    # Cleanup
    for bot in test_bots:
        await sync_to_async(bot.delete)()
    await sync_to_async(BotTargetCooldown.objects.filter(user_bot_name__in=user_bots).delete)()
    print("🧹 Cleaned up test data")


if __name__ == "__main__":
    try:
        asyncio.run(test_cooldown_system())
        asyncio.run(test_multiple_bots())
        print("\n✅ All tests completed successfully!")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
