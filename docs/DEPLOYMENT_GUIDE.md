# Deployment Guide

Complete guide for deploying SaverAPI to production environments.

## 🎯 Overview

This guide covers deployment strategies for SaverAPI, including Docker, traditional server deployment, and cloud platforms.

## 📋 Prerequisites

- Python 3.10+
- PostgreSQL 12+
- Redis 6+
- Nginx (for reverse proxy)
- SSL certificate (for HTTPS)
- Telegram API credentials

## 🐳 Docker Deployment (Recommended)

### 1. Docker Compose Setup

Create `docker-compose.prod.yml`:

```yaml
version: '3.8'

services:
  web:
    build: 
      context: .
      dockerfile: Dockerfile.prod
    ports:
      - "8000:8000"
    environment:
      - DJANGO_SETTINGS_MODULE=backend.settings_prod
      - DATABASE_URL=postgresql://saverapi:${DB_PASSWORD}@db:5432/saverapi
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - X_AUTH_TOKEN=${X_AUTH_TOKEN}
      - API_ID=${API_ID}
      - API_HASH=${API_HASH}
    depends_on:
      - db
      - redis
    volumes:
      - static_volume:/app/staticfiles
      - media_volume:/app/media
      - ./sessions:/app/sessions
    restart: unless-stopped

  worker:
    build: 
      context: .
      dockerfile: Dockerfile.prod
    command: python manage.py run_user_bots
    environment:
      - DJANGO_SETTINGS_MODULE=backend.settings_prod
      - DATABASE_URL=postgresql://saverapi:${DB_PASSWORD}@db:5432/saverapi
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - API_ID=${API_ID}
      - API_HASH=${API_HASH}
    depends_on:
      - db
      - redis
    volumes:
      - ./sessions:/app/sessions
    restart: unless-stopped

  db:
    image: postgres:13
    environment:
      POSTGRES_DB: saverapi
      POSTGRES_USER: saverapi
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:6-alpine
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - static_volume:/var/www/static
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - web
    restart: unless-stopped

volumes:
  postgres_data:
  static_volume:
  media_volume:
```

### 2. Production Dockerfile

Create `Dockerfile.prod`:

```dockerfile
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Set work directory
WORKDIR /app

# Install system dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        postgresql-client \
        build-essential \
        libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy project
COPY . .

# Create sessions directory
RUN mkdir -p sessions

# Collect static files
RUN python manage.py collectstatic --noinput --settings=backend.settings_prod

# Create non-root user
RUN adduser --disabled-password --gecos '' appuser
RUN chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE 8000

# Run gunicorn
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "--workers", "3", "--timeout", "120", "backend.wsgi:application"]
```

### 3. Environment Configuration

Create `.env.prod`:

```env
# Django
SECRET_KEY=your-super-secret-key-here
DEBUG=False
ALLOWED_HOSTS=yourdomain.com,api.yourdomain.com

# Database
DB_PASSWORD=your-secure-db-password

# Authentication
X_AUTH_TOKEN=your-api-auth-token

# Telegram
API_ID=your-telegram-api-id
API_HASH=your-telegram-api-hash

# Email (optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password

# Monitoring (optional)
SENTRY_DSN=your-sentry-dsn
```

### 4. Nginx Configuration

Create `nginx.conf`:

```nginx
events {
    worker_connections 1024;
}

http {
    upstream web {
        server web:8000;
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;

    server {
        listen 80;
        server_name yourdomain.com api.yourdomain.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name yourdomain.com api.yourdomain.com;

        # SSL Configuration
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;

        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

        # API endpoints
        location /v1/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://web;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        # Admin interface
        location /admin/ {
            proxy_pass http://web;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Static files
        location /static/ {
            alias /var/www/static/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # Health check
        location /health/ {
            proxy_pass http://web;
            access_log off;
        }
    }
}
```

### 5. Deployment Commands

```bash
# Clone repository
git clone <your-repo-url> saverapi
cd saverapi

# Set up environment
cp .env.example .env.prod
# Edit .env.prod with your values

# Build and start services
docker-compose -f docker-compose.prod.yml up -d --build

# Run migrations
docker-compose -f docker-compose.prod.yml exec web python manage.py migrate

# Create superuser
docker-compose -f docker-compose.prod.yml exec web python manage.py createsuperuser

# Check logs
docker-compose -f docker-compose.prod.yml logs -f
```

## 🖥️ Traditional Server Deployment

### 1. Server Setup (Ubuntu 20.04+)

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install dependencies
sudo apt install -y python3.11 python3.11-venv python3.11-dev \
    postgresql postgresql-contrib redis-server nginx \
    build-essential libpq-dev git supervisor

# Create application user
sudo adduser --system --group --home /opt/saverapi saverapi
```

### 2. Database Setup

```bash
# Switch to postgres user
sudo -u postgres psql

# Create database and user
CREATE DATABASE saverapi;
CREATE USER saverapi WITH PASSWORD 'your-secure-password';
ALTER ROLE saverapi SET client_encoding TO 'utf8';
ALTER ROLE saverapi SET default_transaction_isolation TO 'read committed';
ALTER ROLE saverapi SET timezone TO 'UTC';
GRANT ALL PRIVILEGES ON DATABASE saverapi TO saverapi;
\q
```

### 3. Application Setup

```bash
# Switch to application user
sudo -u saverapi -i

# Clone repository
git clone <your-repo-url> /opt/saverapi/app
cd /opt/saverapi/app

# Create virtual environment
python3.11 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
pip install gunicorn

# Set up environment
cp .env.example .env
# Edit .env with your configuration

# Run migrations
python manage.py migrate --settings=backend.settings_prod

# Collect static files
python manage.py collectstatic --noinput --settings=backend.settings_prod

# Create superuser
python manage.py createsuperuser --settings=backend.settings_prod
```

### 4. Systemd Services

Create `/etc/systemd/system/saverapi.service`:

```ini
[Unit]
Description=SaverAPI Django Application
After=network.target postgresql.service redis.service

[Service]
Type=notify
User=saverapi
Group=saverapi
WorkingDirectory=/opt/saverapi/app
Environment=DJANGO_SETTINGS_MODULE=backend.settings_prod
ExecStart=/opt/saverapi/app/venv/bin/gunicorn \
    --bind unix:/run/saverapi.sock \
    --workers 3 \
    --timeout 120 \
    --user saverapi \
    --group saverapi \
    --access-logfile /var/log/saverapi/access.log \
    --error-logfile /var/log/saverapi/error.log \
    backend.wsgi:application
ExecReload=/bin/kill -s HUP $MAINPID
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target
```

Create `/etc/systemd/system/saverapi-worker.service`:

```ini
[Unit]
Description=SaverAPI Worker Process
After=network.target postgresql.service redis.service

[Service]
Type=simple
User=saverapi
Group=saverapi
WorkingDirectory=/opt/saverapi/app
Environment=DJANGO_SETTINGS_MODULE=backend.settings_prod
ExecStart=/opt/saverapi/app/venv/bin/python manage.py run_user_bots
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target
```

### 5. Nginx Configuration

Create `/etc/nginx/sites-available/saverapi`:

```nginx
server {
    listen 80;
    server_name yourdomain.com api.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com api.yourdomain.com;

    ssl_certificate /etc/ssl/certs/saverapi.crt;
    ssl_certificate_key /etc/ssl/private/saverapi.key;

    location / {
        proxy_pass http://unix:/run/saverapi.sock;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static/ {
        alias /opt/saverapi/app/staticfiles/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### 6. Start Services

```bash
# Create log directory
sudo mkdir -p /var/log/saverapi
sudo chown saverapi:saverapi /var/log/saverapi

# Enable and start services
sudo systemctl enable saverapi saverapi-worker
sudo systemctl start saverapi saverapi-worker

# Enable nginx site
sudo ln -s /etc/nginx/sites-available/saverapi /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx

# Check status
sudo systemctl status saverapi saverapi-worker
```

## ☁️ Cloud Platform Deployment

### AWS Deployment with ECS

1. **Create ECR Repository**
```bash
aws ecr create-repository --repository-name saverapi
```

2. **Build and Push Image**
```bash
# Get login token
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin <account-id>.dkr.ecr.us-east-1.amazonaws.com

# Build and tag
docker build -f Dockerfile.prod -t saverapi .
docker tag saverapi:latest <account-id>.dkr.ecr.us-east-1.amazonaws.com/saverapi:latest

# Push
docker push <account-id>.dkr.ecr.us-east-1.amazonaws.com/saverapi:latest
```

3. **ECS Task Definition**
```json
{
  "family": "saverapi",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "512",
  "memory": "1024",
  "executionRoleArn": "arn:aws:iam::<account-id>:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "saverapi",
      "image": "<account-id>.dkr.ecr.us-east-1.amazonaws.com/saverapi:latest",
      "portMappings": [
        {
          "containerPort": 8000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "DJANGO_SETTINGS_MODULE",
          "value": "backend.settings_prod"
        }
      ],
      "secrets": [
        {
          "name": "SECRET_KEY",
          "valueFrom": "arn:aws:secretsmanager:us-east-1:<account-id>:secret:saverapi/secret-key"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/saverapi",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

### Google Cloud Run Deployment

1. **Build and Deploy**
```bash
# Build image
gcloud builds submit --tag gcr.io/PROJECT-ID/saverapi

# Deploy to Cloud Run
gcloud run deploy saverapi \
    --image gcr.io/PROJECT-ID/saverapi \
    --platform managed \
    --region us-central1 \
    --allow-unauthenticated \
    --set-env-vars DJANGO_SETTINGS_MODULE=backend.settings_prod
```

2. **Cloud SQL Setup**
```bash
# Create Cloud SQL instance
gcloud sql instances create saverapi-db \
    --database-version=POSTGRES_13 \
    --tier=db-f1-micro \
    --region=us-central1

# Create database
gcloud sql databases create saverapi --instance=saverapi-db
```

## 🔧 Production Settings

Create `backend/settings_prod.py`:

```python
from .settings import *
import dj_database_url
import os

# Security
DEBUG = False
ALLOWED_HOSTS = os.environ.get('ALLOWED_HOSTS', '').split(',')

# Database
DATABASES = {
    'default': dj_database_url.parse(os.environ.get('DATABASE_URL'))
}

# Security settings
SECURE_SSL_REDIRECT = True
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True

# Static files
STATIC_ROOT = '/app/staticfiles'
STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'

# Logging
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/var/log/saverapi/django.log',
            'maxBytes': 1024*1024*15,  # 15MB
            'backupCount': 10,
            'formatter': 'verbose',
        },
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['file', 'console'],
        'level': 'INFO',
    },
}

# Cache
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': os.environ.get('REDIS_URL', 'redis://localhost:6379/1'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

# Email (optional)
if os.environ.get('EMAIL_HOST'):
    EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
    EMAIL_HOST = os.environ.get('EMAIL_HOST')
    EMAIL_PORT = int(os.environ.get('EMAIL_PORT', 587))
    EMAIL_USE_TLS = os.environ.get('EMAIL_USE_TLS', 'True').lower() == 'true'
    EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER')
    EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD')
    DEFAULT_FROM_EMAIL = os.environ.get('DEFAULT_FROM_EMAIL', EMAIL_HOST_USER)

# Monitoring (Sentry)
if os.environ.get('SENTRY_DSN'):
    import sentry_sdk
    from sentry_sdk.integrations.django import DjangoIntegration
    
    sentry_sdk.init(
        dsn=os.environ.get('SENTRY_DSN'),
        integrations=[DjangoIntegration()],
        traces_sample_rate=0.1,
        send_default_pii=True
    )
```

## 📊 Monitoring and Maintenance

### Health Checks

Add to `backend/urls.py`:

```python
from django.http import JsonResponse
from django.utils import timezone
from django.db import connection

def health_check(request):
    try:
        # Check database
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        
        # Check Redis
        from django_redis import get_redis_connection
        redis_conn = get_redis_connection("default")
        redis_conn.ping()
        
        return JsonResponse({
            'status': 'healthy',
            'timestamp': timezone.now(),
            'version': '1.0.0'
        })
    except Exception as e:
        return JsonResponse({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': timezone.now()
        }, status=503)

urlpatterns = [
    path('health/', health_check),
    # ... other patterns
]
```

### Backup Strategy

```bash
#!/bin/bash
# backup.sh

# Database backup
pg_dump -h localhost -U saverapi saverapi > /backups/saverapi_$(date +%Y%m%d_%H%M%S).sql

# Session files backup
tar -czf /backups/sessions_$(date +%Y%m%d_%H%M%S).tar.gz sessions/

# Keep only last 7 days of backups
find /backups -name "*.sql" -mtime +7 -delete
find /backups -name "*.tar.gz" -mtime +7 -delete
```

### Log Rotation

Create `/etc/logrotate.d/saverapi`:

```
/var/log/saverapi/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 saverapi saverapi
    postrotate
        systemctl reload saverapi
    endscript
}
```

## 🔒 Security Checklist

- [ ] Use HTTPS with valid SSL certificates
- [ ] Set strong SECRET_KEY and X_AUTH_TOKEN
- [ ] Configure firewall (only ports 80, 443, 22)
- [ ] Regular security updates
- [ ] Database access restrictions
- [ ] Rate limiting configured
- [ ] Security headers enabled
- [ ] Log monitoring setup
- [ ] Backup strategy implemented
- [ ] Session files secured

## 🚨 Troubleshooting

### Common Issues

1. **502 Bad Gateway**
   - Check if gunicorn is running
   - Verify socket permissions
   - Check nginx configuration

2. **Database Connection Errors**
   - Verify DATABASE_URL
   - Check PostgreSQL service status
   - Confirm network connectivity

3. **Redis Connection Issues**
   - Check Redis service status
   - Verify REDIS_URL configuration
   - Test Redis connectivity

4. **User Bot Authentication Failures**
   - Verify API_ID and API_HASH
   - Check session file permissions
   - Confirm Telegram account status

### Log Analysis

```bash
# Application logs
sudo journalctl -u saverapi -f

# Worker logs
sudo journalctl -u saverapi-worker -f

# Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# Database logs
sudo tail -f /var/log/postgresql/postgresql-13-main.log
```
