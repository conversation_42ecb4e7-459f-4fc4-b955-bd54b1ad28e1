# API Reference

Complete API documentation for SaverAPI endpoints.

## Base Information

- **Base URL**: `http://localhost:8000/v1/`
- **Authentication**: X-AUTH header required
- **Content-Type**: `application/json`
- **Response Format**: JSON

## Authentication

All API requests must include the `X-AUTH` header:

```http
X-AUTH: your-auth-token-here
```

**Error Response (401 Unauthorized):**
```json
{
    "error": "Authentication required",
    "message": "X-AUTH header is missing or invalid"
}
```

## Endpoints

### 1. Create Download Task

Creates a new media download task and adds it to the processing queue.

**Endpoint:** `POST /v1/media-dl/`

#### Request

**Headers:**
```http
Content-Type: application/json
X-AUTH: your-auth-token
```

**Body Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `url` | string | Yes | URL of the media to download |
| `hosting` | string | Yes | Platform name (`instagram`, `tiktok`, `youtube`) |
| `bot_token` | string | Yes | Telegram bot token for downloading |
| `chat_id` | string | Yes | Telegram chat ID where media will be sent |
| `bot_username` | string | No | Bot username (auto-fetched if not provided) |

**Example Request:**
```json
{
    "url": "https://www.instagram.com/p/CXXXXXXXXXx/",
    "hosting": "instagram",
    "bot_token": "*********0:ABCdefGHIjklMNOpqrsTUVwxyz",
    "chat_id": "*********",
    "bot_username": "@my_download_bot"
}
```

#### Response

**Success (201 Created):**
```json
{
    "task_id": "550e8400-e29b-41d4-a716-************",
    "status": "pending",
    "message": "Task created successfully"
}
```

**Error (400 Bad Request):**
```json
{
    "url": ["This field is required."],
    "bot_token": ["Invalid bot token or unable to fetch bot info: Bot not found"]
}
```

#### Validation Rules

- **URL**: Must be a valid URL format
- **Hosting**: Must be one of: `instagram`, `tiktok`, `youtube`
- **Bot Token**: Must be a valid Telegram bot token
- **Chat ID**: Must be a valid Telegram chat ID
- **Bot Username**: If not provided, will be auto-fetched using the bot token

### 2. Check Task Status

Retrieves the current status and details of a download task.

**Endpoint:** `GET /v1/check/{task_id}/status`

#### Request

**Headers:**
```http
X-AUTH: your-auth-token
```

**Path Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `task_id` | UUID | Yes | Unique task identifier |

**Example Request:**
```http
GET /v1/check/550e8400-e29b-41d4-a716-************/status
```

#### Response

**Success (200 OK):**
```json
{
    "task_id": "550e8400-e29b-41d4-a716-************",
    "status": "completed",
    "url": "https://www.instagram.com/p/CXXXXXXXXXx/",
    "hosting": "instagram",
    "bot_token": "*********0:ABCdefGHIjklMNOpqrsTUVwxyz",
    "bot_username": "@my_download_bot",
    "chat_id": "*********",
    "created_at": "2024-01-15T10:30:00.123456Z",
    "updated_at": "2024-01-15T10:32:15.789012Z",
    "account_name": "user_account_1",
    "message_id": 12345,
    "file_id": "BAADBAADrwADBREAAWdXAAE...",
    "error_message": null
}
```

**Error (404 Not Found):**
```json
{
    "error": "Task not found",
    "message": "No task found with the provided task_id"
}
```

#### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `task_id` | UUID | Unique task identifier |
| `status` | string | Current task status |
| `url` | string | Original media URL |
| `hosting` | string | Platform name |
| `bot_token` | string | Telegram bot token used |
| `bot_username` | string | Bot username |
| `chat_id` | string | Telegram chat ID |
| `created_at` | datetime | Task creation timestamp |
| `updated_at` | datetime | Last update timestamp |
| `account_name` | string | User account that processed the task |
| `message_id` | integer | Telegram message ID (if completed) |
| `file_id` | string | Telegram file ID (if completed) |
| `error_message` | string | Error details (if failed) |

## Status Values

| Status | Description |
|--------|-------------|
| `pending` | Task created and waiting in queue |
| `processing` | Task is being processed by a user bot |
| `completed` | Media successfully downloaded and forwarded |
| `failed` | Task failed with error (check `error_message`) |

## Error Handling

### Common Error Responses

**400 Bad Request:**
```json
{
    "field_name": ["Error message for this field"],
    "another_field": ["Another error message"]
}
```

**401 Unauthorized:**
```json
{
    "error": "Authentication required",
    "message": "X-AUTH header is missing or invalid"
}
```

**404 Not Found:**
```json
{
    "error": "Resource not found",
    "message": "The requested resource was not found"
}
```

**500 Internal Server Error:**
```json
{
    "error": "Internal server error",
    "message": "An unexpected error occurred"
}
```

### Bot Token Validation Errors

**Invalid Bot Token:**
```json
{
    "bot_token": ["Invalid bot token or unable to fetch bot info: Unauthorized"]
}
```

**Bot Username Fetch Failed:**
```json
{
    "bot_token": ["Could not retrieve bot username from token. Please provide bot_username manually."]
}
```

## Rate Limiting

- **Default Limit**: 100 requests per minute per IP
- **Burst Limit**: 10 requests per second

**Rate Limit Headers:**
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642248000
```

**Rate Limit Exceeded (429):**
```json
{
    "error": "Rate limit exceeded",
    "message": "Too many requests. Please try again later.",
    "retry_after": 60
}
```

## Examples

### Complete Workflow Example

```python
import requests
import time

# Configuration
API_BASE = "http://localhost:8000/v1"
AUTH_TOKEN = "your-auth-token"
headers = {"X-AUTH": AUTH_TOKEN, "Content-Type": "application/json"}

# Step 1: Create download task
task_data = {
    "url": "https://www.instagram.com/p/example/",
    "hosting": "instagram",
    "bot_token": "*********0:ABCdefGHIjklMNOpqrsTUVwxyz",
    "chat_id": "*********"
}

response = requests.post(f"{API_BASE}/media-dl/", json=task_data, headers=headers)
task = response.json()
task_id = task["task_id"]

print(f"Task created: {task_id}")

# Step 2: Poll for completion
while True:
    status_response = requests.get(f"{API_BASE}/check/{task_id}/status", headers=headers)
    status = status_response.json()
    
    print(f"Status: {status['status']}")
    
    if status["status"] in ["completed", "failed"]:
        break
    
    time.sleep(5)  # Wait 5 seconds before checking again

# Step 3: Handle result
if status["status"] == "completed":
    print(f"Download completed! File ID: {status['file_id']}")
    print(f"Message ID: {status['message_id']}")
else:
    print(f"Download failed: {status['error_message']}")
```

### Batch Processing Example

```python
import requests
import asyncio
import aiohttp

async def create_download_task(session, url, bot_token, chat_id):
    """Create a single download task"""
    task_data = {
        "url": url,
        "hosting": "instagram",
        "bot_token": bot_token,
        "chat_id": chat_id
    }
    
    async with session.post("/media-dl/", json=task_data) as response:
        return await response.json()

async def check_task_status(session, task_id):
    """Check status of a task"""
    async with session.get(f"/check/{task_id}/status") as response:
        return await response.json()

async def process_multiple_downloads(urls, bot_token, chat_id):
    """Process multiple downloads concurrently"""
    headers = {"X-AUTH": "your-auth-token", "Content-Type": "application/json"}
    
    async with aiohttp.ClientSession(
        base_url="http://localhost:8000/v1",
        headers=headers
    ) as session:
        
        # Create all tasks
        tasks = []
        for url in urls:
            task = await create_download_task(session, url, bot_token, chat_id)
            tasks.append(task["task_id"])
        
        # Monitor all tasks
        completed_tasks = []
        while len(completed_tasks) < len(tasks):
            for task_id in tasks:
                if task_id not in completed_tasks:
                    status = await check_task_status(session, task_id)
                    if status["status"] in ["completed", "failed"]:
                        completed_tasks.append(task_id)
                        print(f"Task {task_id}: {status['status']}")
            
            await asyncio.sleep(2)

# Usage
urls = [
    "https://www.instagram.com/p/example1/",
    "https://www.instagram.com/p/example2/",
    "https://www.instagram.com/p/example3/"
]

asyncio.run(process_multiple_downloads(urls, "bot_token", "chat_id"))
```

## SDK and Client Libraries

### Python SDK Example

```python
class SaverAPIClient:
    def __init__(self, base_url, auth_token):
        self.base_url = base_url.rstrip('/')
        self.headers = {
            'X-AUTH': auth_token,
            'Content-Type': 'application/json'
        }
    
    def create_download(self, url, hosting, bot_token, chat_id, bot_username=None):
        """Create a new download task"""
        data = {
            'url': url,
            'hosting': hosting,
            'bot_token': bot_token,
            'chat_id': chat_id
        }
        if bot_username:
            data['bot_username'] = bot_username
        
        response = requests.post(
            f"{self.base_url}/v1/media-dl/",
            json=data,
            headers=self.headers
        )
        response.raise_for_status()
        return response.json()
    
    def get_task_status(self, task_id):
        """Get task status"""
        response = requests.get(
            f"{self.base_url}/v1/check/{task_id}/status",
            headers=self.headers
        )
        response.raise_for_status()
        return response.json()
    
    def wait_for_completion(self, task_id, timeout=300, poll_interval=5):
        """Wait for task completion with timeout"""
        import time
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            status = self.get_task_status(task_id)
            if status['status'] in ['completed', 'failed']:
                return status
            time.sleep(poll_interval)
        
        raise TimeoutError(f"Task {task_id} did not complete within {timeout} seconds")

# Usage
client = SaverAPIClient("http://localhost:8000", "your-auth-token")

# Create and wait for download
task = client.create_download(
    url="https://www.instagram.com/p/example/",
    hosting="instagram",
    bot_token="your-bot-token",
    chat_id="your-chat-id"
)

result = client.wait_for_completion(task['task_id'])
print(f"Download result: {result['status']}")
```
