# Architecture Documentation

This document provides a comprehensive overview of the SaverAPI system architecture, components, and data flow.

## 🏗️ System Overview

SaverAPI is a distributed system designed to download media content from social media platforms using Telegram bots as intermediaries. The system follows a microservices-like architecture with clear separation of concerns.

```mermaid
graph TB
    Client[Client Applications] --> API[FastAPI Server]
    API --> Auth[Authentication Layer]
    API --> DB[(PostgreSQL Database)]
    API --> Redis[(Redis Queue)]
    
    Redis --> Worker[Background Workers]
    Worker --> UserBots[User Bots<br/>Pyrogram Clients]
    UserBots --> TargetBots[Target Bots<br/>Telegram Bots]
    
    TargetBots --> Platforms[Social Media Platforms<br/>Instagram, TikTok, YouTube]
    
    Worker --> DB
    UserBots --> DB
```

## 🔧 Core Components

### 1. API Layer (FastAPI)

The API layer serves as the main entry point for client applications and handles:

- **Request Validation**: Using Pydantic models for type checking and validation
- **Authentication**: X-AUTH header-based authentication
- **Rate Limiting**: Preventing abuse and ensuring fair usage
- **Response Formatting**: Consistent JSON responses
- **Documentation**: Auto-generated OpenAPI/Swagger documentation

**Key Features:**
- Async/await support for better performance
- Type hints throughout the codebase
- Automatic request/response validation
- Built-in error handling and logging

### 2. Database Layer (PostgreSQL)

The database layer provides persistent storage for:

- **Download Tasks**: Task metadata, status, and results
- **Bot Configurations**: Target bot settings and capabilities
- **User Accounts**: Telegram user account information
- **Audit Logs**: System activity and error tracking

**Schema Design:**
```sql
-- Download Tasks
CREATE TABLE download_tasks (
    id SERIAL PRIMARY KEY,
    task_id UUID UNIQUE NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    url VARCHAR(255) NOT NULL,
    hosting VARCHAR(50) DEFAULT 'instagram',
    bot_token VARCHAR(255) NOT NULL,
    bot_username VARCHAR(100),
    chat_id VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    error_message TEXT,
    account_name VARCHAR(100),
    message_id BIGINT,
    file_id TEXT
);

-- Target Bots
CREATE TABLE target_bots (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    username VARCHAR(100) NOT NULL,
    is_active BOOLEAN DEFAULT FALSE,
    can_dl_from_insta BOOLEAN DEFAULT FALSE,
    can_dl_from_tiktok BOOLEAN DEFAULT FALSE,
    can_dl_from_youtube BOOLEAN DEFAULT FALSE
);
```

### 3. Message Queue (Redis)

Redis serves as the message broker for asynchronous task processing:

- **Task Queue**: Stores pending download tasks
- **Caching**: Bot information and temporary data
- **Session Storage**: User bot session data
- **Rate Limiting**: Request throttling data

**Queue Structure:**
```json
{
    "task_id": "uuid-string",
    "url": "https://instagram.com/p/example/",
    "bot_username": "@download_bot",
    "hosting": "instagram",
    "bot_token": "bot-token",
    "chat_id": "chat-id"
}
```

### 4. Background Workers

Background workers process tasks from the Redis queue:

- **Task Processing**: Handles download requests asynchronously
- **Status Updates**: Updates task status in the database
- **Error Handling**: Manages failures and retries
- **Load Balancing**: Distributes tasks across available user bots

**Worker Architecture:**
```python
async def process_queue():
    while True:
        task = await redis.brpop("download_queue")
        await process_download_task(task)
        
async def process_download_task(task):
    # Update status to processing
    await update_task_status(task.id, "processing")
    
    # Find available user bot
    user_bot = await get_available_user_bot()
    
    # Send download request
    result = await user_bot.send_download_request(task)
    
    # Update final status
    await update_task_status(task.id, result.status)
```

### 5. User Bots (Pyrogram Clients)

User bots are Telegram user accounts that act as intermediaries:

- **Message Forwarding**: Send URLs to target bots
- **Response Handling**: Receive and process bot responses
- **Session Management**: Maintain Telegram session state
- **Load Distribution**: Balance requests across multiple accounts

**Bot Management:**
```python
class UserBotManager:
    def __init__(self):
        self.bots = {}
        self.load_balancer = RoundRobinBalancer()
    
    async def get_available_bot(self):
        return self.load_balancer.get_next_bot()
    
    async def send_request(self, bot, url, target_bot):
        return await bot.send_message(target_bot, url)
```

### 6. Target Bots (Telegram Bots)

Target bots are specialized Telegram bots that perform actual downloads:

- **Platform Support**: Each bot supports specific platforms
- **Download Logic**: Handles media extraction and processing
- **Response Format**: Returns standardized media files
- **Rate Limiting**: Manages platform-specific limits

## 📊 Data Flow

### 1. Download Request Flow

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant DB
    participant Redis
    participant Worker
    participant UserBot
    participant TargetBot
    participant Platform

    Client->>API: POST /v1/media-dl/
    API->>API: Validate request
    API->>DB: Create download task
    API->>Redis: Add task to queue
    API->>Client: Return task_id
    
    Worker->>Redis: Poll for tasks
    Redis->>Worker: Return task
    Worker->>DB: Update status to "processing"
    Worker->>UserBot: Send download request
    UserBot->>TargetBot: Forward URL
    TargetBot->>Platform: Download media
    Platform->>TargetBot: Return media
    TargetBot->>UserBot: Send media file
    UserBot->>Worker: Return file info
    Worker->>DB: Update status to "completed"
```

### 2. Status Check Flow

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant DB

    Client->>API: GET /v1/check/{task_id}/status
    API->>API: Validate auth
    API->>DB: Query task status
    DB->>API: Return task data
    API->>Client: Return status response
```

## 🔒 Security Architecture

### 1. Authentication & Authorization

- **API Key Authentication**: X-AUTH header validation
- **Token Validation**: Telegram bot token verification
- **Rate Limiting**: Per-IP and per-user limits
- **Input Sanitization**: URL and parameter validation

### 2. Data Protection

- **Encryption**: Sensitive data encrypted at rest
- **Secure Communication**: HTTPS/TLS for all API calls
- **Token Security**: Bot tokens stored securely
- **Session Protection**: User bot sessions encrypted

### 3. Network Security

```mermaid
graph LR
    Internet[Internet] --> LB[Load Balancer]
    LB --> WAF[Web Application Firewall]
    WAF --> API[API Server]
    API --> DB[(Database)]
    API --> Redis[(Redis)]
    
    subgraph "Private Network"
        API
        DB
        Redis
        Worker[Background Workers]
    end
```

## 🚀 Scalability Design

### 1. Horizontal Scaling

- **API Servers**: Multiple FastAPI instances behind load balancer
- **Background Workers**: Scalable worker processes
- **Database**: Read replicas for query scaling
- **Redis**: Redis Cluster for high availability

### 2. Load Balancing

```python
class LoadBalancer:
    def __init__(self):
        self.strategies = {
            'round_robin': RoundRobinStrategy(),
            'least_connections': LeastConnectionsStrategy(),
            'weighted': WeightedStrategy()
        }
    
    def distribute_task(self, task):
        strategy = self.strategies['round_robin']
        worker = strategy.select_worker()
        return worker.process(task)
```

### 3. Caching Strategy

- **L1 Cache**: In-memory application cache
- **L2 Cache**: Redis distributed cache
- **Database Cache**: Query result caching
- **CDN**: Static content delivery

## 📈 Performance Optimization

### 1. Database Optimization

- **Indexing**: Strategic indexes on frequently queried columns
- **Connection Pooling**: Efficient database connection management
- **Query Optimization**: Optimized SQL queries and ORM usage
- **Partitioning**: Table partitioning for large datasets

```sql
-- Indexes for performance
CREATE INDEX idx_download_tasks_task_id ON download_tasks(task_id);
CREATE INDEX idx_download_tasks_status ON download_tasks(status);
CREATE INDEX idx_download_tasks_created_at ON download_tasks(created_at);
```

### 2. API Performance

- **Async Processing**: Non-blocking I/O operations
- **Response Compression**: Gzip compression for responses
- **Connection Pooling**: HTTP client connection reuse
- **Batch Operations**: Bulk database operations

### 3. Worker Optimization

- **Concurrent Processing**: Multiple tasks processed simultaneously
- **Resource Management**: Efficient memory and CPU usage
- **Queue Optimization**: Prioritized task processing
- **Error Recovery**: Automatic retry mechanisms

## 🔄 Deployment Architecture

### 1. Container Architecture

```dockerfile
# Multi-stage build for optimization
FROM python:3.11-slim as builder
WORKDIR /app
COPY requirements.txt .
RUN pip install --user -r requirements.txt

FROM python:3.11-slim
WORKDIR /app
COPY --from=builder /root/.local /root/.local
COPY . .
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 2. Orchestration (Kubernetes)

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: saverapi
spec:
  replicas: 3
  selector:
    matchLabels:
      app: saverapi
  template:
    metadata:
      labels:
        app: saverapi
    spec:
      containers:
      - name: api
        image: saverapi:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: saverapi-secrets
              key: database-url
```

### 3. Service Mesh

- **Istio**: Service-to-service communication
- **Circuit Breakers**: Fault tolerance
- **Observability**: Distributed tracing
- **Security**: mTLS between services

## 📊 Monitoring & Observability

### 1. Metrics Collection

- **Application Metrics**: Request rates, response times, error rates
- **System Metrics**: CPU, memory, disk usage
- **Business Metrics**: Download success rates, user activity
- **Custom Metrics**: Bot performance, queue lengths

### 2. Logging Strategy

```python
import structlog

logger = structlog.get_logger()

async def process_download(task_id: str):
    logger.info("Processing download", task_id=task_id)
    try:
        result = await download_media(task_id)
        logger.info("Download completed", task_id=task_id, result=result)
    except Exception as e:
        logger.error("Download failed", task_id=task_id, error=str(e))
```

### 3. Health Checks

```python
@app.get("/health")
async def health_check():
    checks = {
        "database": await check_database(),
        "redis": await check_redis(),
        "telegram_api": await check_telegram_api()
    }
    
    status = "healthy" if all(checks.values()) else "unhealthy"
    return {"status": status, "checks": checks}
```

## 🔧 Configuration Management

### 1. Environment-based Configuration

```python
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    database_url: str
    redis_url: str
    telegram_api_id: int
    telegram_api_hash: str
    
    class Config:
        env_file = ".env"
```

### 2. Feature Flags

```python
class FeatureFlags:
    ENABLE_NEW_DOWNLOAD_ALGORITHM = True
    ENABLE_BATCH_PROCESSING = False
    MAX_CONCURRENT_DOWNLOADS = 10
```

## 🚨 Error Handling & Recovery

### 1. Error Classification

- **Transient Errors**: Network timeouts, temporary API failures
- **Permanent Errors**: Invalid URLs, authentication failures
- **System Errors**: Database failures, service unavailability

### 2. Retry Strategy

```python
from tenacity import retry, stop_after_attempt, wait_exponential

@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10)
)
async def download_with_retry(url: str):
    return await download_media(url)
```

### 3. Circuit Breaker Pattern

```python
class CircuitBreaker:
    def __init__(self, failure_threshold=5, timeout=60):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
```

## 📝 API Design Principles

### 1. RESTful Design

- **Resource-based URLs**: `/v1/downloads/{id}`
- **HTTP Methods**: GET, POST, PUT, DELETE
- **Status Codes**: Appropriate HTTP status codes
- **Consistent Response Format**: Standardized JSON responses

### 2. Versioning Strategy

- **URL Versioning**: `/v1/`, `/v2/`
- **Backward Compatibility**: Maintain older versions
- **Deprecation Policy**: Clear deprecation timeline

### 3. Error Response Format

```json
{
    "error": {
        "code": "INVALID_URL",
        "message": "The provided URL is not valid",
        "details": {
            "field": "url",
            "value": "invalid-url"
        }
    }
}
```

## 🔮 Future Enhancements

### 1. Planned Features

- **WebSocket Support**: Real-time status updates
- **Batch Downloads**: Multiple URLs in single request
- **Webhook Notifications**: Callback URLs for completion
- **Advanced Analytics**: Download statistics and insights

### 2. Technology Upgrades

- **GraphQL API**: Alternative to REST API
- **Event Sourcing**: Event-driven architecture
- **Machine Learning**: Smart bot selection and optimization
- **Blockchain Integration**: Decentralized storage options

### 3. Scalability Improvements

- **Auto-scaling**: Dynamic resource allocation
- **Multi-region Deployment**: Global content delivery
- **Edge Computing**: Processing closer to users
- **Serverless Functions**: Event-driven processing

This architecture provides a solid foundation for the SaverAPI system while maintaining flexibility for future enhancements and scaling requirements.
