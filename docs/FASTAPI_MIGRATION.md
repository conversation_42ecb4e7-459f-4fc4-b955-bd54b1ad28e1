# FastAPI Migration Guide

This document outlines the process for migrating the SaverAPI project from Django to FastAPI.

## 🚀 Why FastAPI?

FastAPI offers several advantages for this project:

1. **Performance**: FastAPI is built on Starlette and Uvicorn, providing high performance
2. **Native Async Support**: Better suited for the async nature of the Telegram bot operations
3. **Type Hints**: Built-in type checking with Pydantic models
4. **Automatic Documentation**: OpenAPI and Swagger UI out of the box
5. **Simplified Development**: Less boilerplate code than Django
6. **Modern Python**: Designed for Python 3.7+ with all modern features

## 📋 Migration Plan

### 1. Project Structure

The recommended FastAPI project structure:

```
saverapi/
├── app/
│   ├── __init__.py
│   ├── main.py                 # FastAPI application entry point
│   ├── core/
│   │   ├── __init__.py
│   │   ├── config.py           # Settings and configuration
│   │   ├── security.py         # Authentication and security
│   │   └── logging.py          # Logging configuration
│   ├── api/
│   │   ├── __init__.py
│   │   ├── deps.py             # Dependency injection
│   │   ├── routes/
│   │   │   ├── __init__.py
│   │   │   ├── downloads.py    # Download endpoints
│   │   │   └── status.py       # Status check endpoints
│   ├── models/
│   │   ├── __init__.py
│   │   ├── download.py         # Pydantic models for downloads
│   │   └── bot.py              # Pydantic models for bots
│   ├── db/
│   │   ├── __init__.py
│   │   ├── session.py          # Database session
│   │   └── models/
│   │       ├── __init__.py
│   │       ├── download.py     # SQLAlchemy models for downloads
│   │       └── bot.py          # SQLAlchemy models for bots
│   ├── services/
│   │   ├── __init__.py
│   │   ├── download.py         # Download business logic
│   │   └── bot.py              # Bot management logic
│   ├── utils/
│   │   ├── __init__.py
│   │   └── telegram.py         # Telegram utilities
│   └── worker/
│       ├── __init__.py
│       └── processor.py        # Background task processor
├── alembic/                    # Database migrations
├── tests/                      # Test suite
├── .env                        # Environment variables
├── .env.example                # Example environment file
├── requirements.txt            # Dependencies
├── Dockerfile                  # Docker configuration
└── docker-compose.yml          # Docker Compose configuration
```

### 2. Dependencies

Create a new `requirements.txt` file:

```
# FastAPI and ASGI server
fastapi==0.110.0
uvicorn[standard]==0.27.1
pydantic==2.6.1
pydantic-settings==2.2.1

# Database
sqlalchemy==2.0.27
alembic==1.13.1
asyncpg==0.29.0

# Redis
redis==6.2.0
aioredis==2.0.1

# Telegram
pyrogram==2.0.106
tgcrypto==1.2.5
python-telegram-bot==20.7

# Utilities
python-dotenv==1.0.1
httpx==0.26.0
python-multipart==0.0.9
tenacity==8.2.3
pytz==2024.1

# Testing
pytest==8.0.0
pytest-asyncio==0.23.5
pytest-cov==4.1.0

# Development
black==24.2.0
isort==5.13.2
flake8==7.0.0
```

### 3. Core Configuration

#### Settings (`app/core/config.py`)

```python
from pydantic_settings import BaseSettings
from typing import List, Optional
import os


class Settings(BaseSettings):
    """Application settings"""
    
    # API Configuration
    API_V1_STR: str = "/v1"
    PROJECT_NAME: str = "SaverAPI"
    
    # Security
    SECRET_KEY: str
    X_AUTH_TOKEN: str
    
    # CORS
    BACKEND_CORS_ORIGINS: List[str] = ["*"]
    
    # Database
    DATABASE_URL: str
    
    # Redis
    REDIS_URL: str
    
    # Telegram
    API_ID: int
    API_HASH: str
    
    # Logging
    LOG_LEVEL: str = "INFO"
    
    class Config:
        env_file = ".env"
        case_sensitive = True


settings = Settings()
```

#### Security (`app/core/security.py`)

```python
from fastapi import Depends, HTTPException, Security, status
from fastapi.security import APIKeyHeader
from app.core.config import settings

# Define the API key header
api_key_header = APIKeyHeader(name="X-AUTH", auto_error=False)


async def get_api_key(api_key: str = Security(api_key_header)) -> str:
    """Validate API key from X-AUTH header"""
    if not api_key:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required",
            headers={"WWW-Authenticate": "ApiKey"},
        )
    
    if api_key != settings.X_AUTH_TOKEN:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key",
            headers={"WWW-Authenticate": "ApiKey"},
        )
    
    return api_key
```

### 4. Database Models

#### SQLAlchemy Models (`app/db/models/download.py`)

```python
import uuid
from datetime import datetime
from sqlalchemy import Column, String, DateTime, Text, Integer, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from app.db.session import Base


class DownloadTask(Base):
    """Download task model"""
    
    __tablename__ = "download_tasks"
    
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, index=True)
    status = Column(String(20), default="pending")
    url = Column(String(255), nullable=False)
    hosting = Column(String(50), default="instagram")
    bot_token = Column(String(255), nullable=False)
    bot_username = Column(String(100), nullable=True)
    chat_id = Column(String(100), nullable=False)
    
    # Tracking fields
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Error handling
    error_message = Column(Text, nullable=True)
    
    # Processing details
    account_name = Column(String(100), nullable=True)
    message_id = Column(Integer, nullable=True)
    file_id = Column(Text, nullable=True)
```

#### Pydantic Models (`app/models/download.py`)

```python
from pydantic import BaseModel, Field, HttpUrl, validator
from typing import Optional
from datetime import datetime
import uuid


class DownloadBase(BaseModel):
    """Base model for download requests"""
    url: HttpUrl
    hosting: str = Field(default="instagram")
    bot_token: str
    chat_id: str
    bot_username: Optional[str] = None
    
    @validator("hosting")
    def validate_hosting(cls, v):
        allowed = ["instagram", "tiktok", "youtube"]
        if v.lower() not in allowed:
            raise ValueError(f"Hosting must be one of: {', '.join(allowed)}")
        return v.lower()


class DownloadCreate(DownloadBase):
    """Model for creating a download task"""
    pass


class DownloadResponse(BaseModel):
    """Response model for download creation"""
    task_id: uuid.UUID
    status: str
    message: str = "Task created successfully"


class DownloadStatus(BaseModel):
    """Model for download task status"""
    task_id: uuid.UUID
    status: str
    url: HttpUrl
    hosting: str
    created_at: datetime
    updated_at: datetime
    account_name: Optional[str] = None
    message_id: Optional[int] = None
    file_id: Optional[str] = None
    error_message: Optional[str] = None
    
    class Config:
        orm_mode = True
```

### 5. Database Setup

#### Database Session (`app/db/session.py`)

```python
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import declarative_base, sessionmaker
from app.core.config import settings

# Create async engine
engine = create_async_engine(
    settings.DATABASE_URL,
    echo=False,
    future=True,
)

# Create async session factory
AsyncSessionLocal = sessionmaker(
    engine, class_=AsyncSession, expire_on_commit=False
)

# Create declarative base
Base = declarative_base()


# Dependency for getting DB session
async def get_db():
    """Dependency for getting async DB session"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()
```

### 6. API Routes

#### Download Endpoints (`app/api/routes/downloads.py`)

```python
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
import uuid
import json

from app.core.security import get_api_key
from app.db.session import get_db
from app.db.models.download import DownloadTask
from app.models.download import DownloadCreate, DownloadResponse, DownloadStatus
from app.services.download import validate_bot_token, get_bot_username
from app.utils.redis import get_redis_connection

router = APIRouter()


@router.post(
    "/media-dl/",
    response_model=DownloadResponse,
    status_code=status.HTTP_201_CREATED,
    dependencies=[Depends(get_api_key)],
)
async def create_download_task(
    download: DownloadCreate, db: AsyncSession = Depends(get_db)
):
    """Create a new download task"""
    
    # Validate bot token and get username if not provided
    if not download.bot_username:
        bot_username = await get_bot_username(download.bot_token)
        if bot_username:
            download.bot_username = bot_username
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Could not retrieve bot username from token. Please provide bot_username manually.",
            )
    
    # Create task in database
    task = DownloadTask(
        url=str(download.url),
        hosting=download.hosting,
        bot_token=download.bot_token,
        bot_username=download.bot_username,
        chat_id=download.chat_id,
        status="pending",
    )
    
    db.add(task)
    await db.commit()
    await db.refresh(task)
    
    # Add to Redis queue
    redis = await get_redis_connection()
    queue_data = {
        "task_id": str(task.task_id),
        "url": str(download.url),
        "bot_username": download.bot_username,
        "hosting": download.hosting,
        "bot_token": download.bot_token,
        "chat_id": download.chat_id,
    }
    
    await redis.lpush("download_queue", json.dumps(queue_data))
    
    return {
        "task_id": task.task_id,
        "status": task.status,
        "message": "Task created successfully",
    }


@router.get(
    "/check/{task_id}/status",
    response_model=DownloadStatus,
    dependencies=[Depends(get_api_key)],
)
async def check_task_status(task_id: uuid.UUID, db: AsyncSession = Depends(get_db)):
    """Check the status of a download task"""
    
    # Query task from database
    result = await db.execute(
        select(DownloadTask).where(DownloadTask.task_id == task_id)
    )
    task = result.scalars().first()
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Task with ID {task_id} not found",
        )
    
    return task
```

### 7. Main Application

#### FastAPI App (`app/main.py`)

```python
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import logging

from app.core.config import settings
from app.api.routes import downloads

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)

# Create FastAPI app
app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url=f"{settings.API_V1_STR}/docs",
    redoc_url=f"{settings.API_V1_STR}/redoc",
)

# Configure CORS
if settings.BACKEND_CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

# Include routers
app.include_router(downloads.router, prefix=settings.API_V1_STR)


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy"}


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True)
```

### 8. Redis Utilities

#### Redis Connection (`app/utils/redis.py`)

```python
import aioredis
from app.core.config import settings

_redis = None


async def get_redis_connection():
    """Get Redis connection"""
    global _redis
    
    if _redis is None:
        _redis = await aioredis.from_url(
            settings.REDIS_URL,
            encoding="utf-8",
            decode_responses=True,
        )
    
    return _redis


async def close_redis_connection():
    """Close Redis connection"""
    global _redis
    
    if _redis is not None:
        await _redis.close()
        _redis = None
```

### 9. Telegram Utilities

#### Telegram Bot Utilities (`app/utils/telegram.py`)

```python
import httpx
from typing import Optional, Dict, Any
import json
import asyncio
from app.core.config import settings


class TelegramBotUtils:
    """Utilities for working with Telegram bots"""
    
    @staticmethod
    async def get_bot_info(bot_token: str) -> Optional[Dict[str, Any]]:
        """
        Get bot information from Telegram Bot API using bot token
        
        Args:
            bot_token: Telegram bot token
            
        Returns:
            Dict containing bot information or None if failed
        """
        if not bot_token:
            raise ValueError("Bot token is required")
        
        url = f"https://api.telegram.org/bot{bot_token}/getMe"
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(url, timeout=10.0)
                data = response.json()
                
                if response.status_code == 200 and data.get("ok"):
                    return data.get("result")
                return None
            except Exception:
                return None
    
    @staticmethod
    async def get_bot_username(bot_token: str) -> Optional[str]:
        """
        Get bot username from bot token
        
        Args:
            bot_token: Telegram bot token
            
        Returns:
            Bot username with @ prefix or None if not found
        """
        try:
            bot_info = await TelegramBotUtils.get_bot_info(bot_token)
            if bot_info and bot_info.get('username'):
                return f"@{bot_info['username']}"
            return None
        except Exception:
            return None
    
    @staticmethod
    async def validate_bot_token(bot_token: str) -> bool:
        """
        Validate if bot token is valid by making a test API call
        
        Args:
            bot_token: Telegram bot token to validate
            
        Returns:
            True if token is valid, False otherwise
        """
        try:
            bot_info = await TelegramBotUtils.get_bot_info(bot_token)
            return bot_info is not None
        except Exception:
            return False


async def get_bot_username_from_token(bot_token: str) -> Optional[str]:
    """
    Convenience function to get bot username from token
    
    Args:
        bot_token: Telegram bot token
        
    Returns:
        Bot username with @ prefix or None if failed
    """
    return await TelegramBotUtils.get_bot_username(bot_token)
```

### 10. Background Worker

#### Task Processor (`app/worker/processor.py`)

```python
import asyncio
import json
import logging
from sqlalchemy import update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
import uuid

from app.db.session import AsyncSessionLocal
from app.db.models.download import DownloadTask
from app.utils.redis import get_redis_connection

logger = logging.getLogger(__name__)


async def update_task_status(task_id: str, status: str, **kwargs):
    """Update task status in database"""
    async with AsyncSessionLocal() as session:
        try:
            # Create update statement
            stmt = (
                update(DownloadTask)
                .where(DownloadTask.task_id == uuid.UUID(task_id))
                .values(status=status, **kwargs)
            )
            
            # Execute update
            await session.execute(stmt)
            await session.commit()
            
            logger.info(f"Updated task {task_id} status to {status}")
        except Exception as e:
            logger.error(f"Error updating task {task_id}: {e}")
            await session.rollback()


async def process_queue():
    """Process download queue from Redis"""
    logger.info("Starting download queue processor")
    
    redis = await get_redis_connection()
    
    while True:
        try:
            # Get task from queue
            task_data = await redis.brpop("download_queue", timeout=1)
            
            if not task_data:
                await asyncio.sleep(1)
                continue
            
            # Parse task data
            _, task_json = task_data
            task = json.loads(task_json)
            task_id = task.get("task_id")
            
            logger.info(f"Processing task {task_id}")
            
            # Update task status to processing
            await update_task_status(task_id, "processing")
            
            # TODO: Implement actual download logic
            # This would involve sending the URL to the appropriate Telegram bot
            # and handling the response
            
            # For now, just simulate processing
            await asyncio.sleep(5)
            
            # Update task as completed
            await update_task_status(
                task_id,
                "completed",
                account_name="test_account",
                message_id=12345,
                file_id="sample_file_id",
            )
            
        except Exception as e:
            logger.error(f"Error processing queue: {e}")
            await asyncio.sleep(5)


async def main():
    """Main worker function"""
    await process_queue()


if __name__ == "__main__":
    asyncio.run(main())
```

### 11. Running the Application

#### Development

```bash
# Run the FastAPI application
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Run the background worker
python -m app.worker.processor
```

#### Production

```bash
# Run the FastAPI application with Gunicorn
gunicorn app.main:app -k uvicorn.workers.UvicornWorker -w 4 --bind 0.0.0.0:8000

# Run the background worker
python -m app.worker.processor
```

## 🔄 Migration Steps

1. **Set up the new project structure**
   - Create the directory structure as outlined above
   - Copy over environment variables and configuration

2. **Create the database models**
   - Define SQLAlchemy models based on Django models
   - Create Pydantic models for request/response validation

3. **Implement API endpoints**
   - Create FastAPI routes for each Django view
   - Implement authentication middleware

4. **Migrate database**
   - Use Alembic to create migration scripts
   - Apply migrations to the database

5. **Implement background worker**
   - Port the Django management command to an async worker
   - Implement Redis queue processing

6. **Test the new implementation**
   - Write unit and integration tests
   - Test API endpoints and worker functionality

7. **Deploy the new application**
   - Update deployment scripts and configurations
   - Deploy the FastAPI application and worker

## 📝 Additional Considerations

### Authentication

FastAPI provides several authentication options:

```python
from fastapi import Depends, FastAPI, HTTPException, status
from fastapi.security import APIKeyHeader

app = FastAPI()
api_key_header = APIKeyHeader(name="X-AUTH", auto_error=False)

async def get_api_key(api_key: str = Depends(api_key_header)):
    if not api_key or api_key != "your-api-key":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API Key",
        )
    return api_key

@app.get("/protected", dependencies=[Depends(get_api_key)])
async def protected_route():
    return {"message": "You have access"}
```

### Database Migrations

Use Alembic for database migrations:

```bash
# Initialize Alembic
alembic init alembic

# Create a migration
alembic revision --autogenerate -m "Initial migration"

# Apply migrations
alembic upgrade head
```

### Testing

FastAPI works well with pytest:

```python
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_health_check():
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json() == {"status": "healthy"}
```

### Documentation

FastAPI automatically generates OpenAPI documentation:

- Swagger UI: `/v1/docs`
- ReDoc: `/v1/redoc`
- OpenAPI JSON: `/v1/openapi.json`

## 🚀 Conclusion

Migrating from Django to FastAPI will provide better performance and a more modern architecture for the SaverAPI project. The async-first approach of FastAPI aligns well with the Telegram bot integration and background processing requirements of the application.

The migration should be done incrementally, with thorough testing at each step to ensure functionality is preserved. Once completed, the new FastAPI implementation will be more maintainable, better documented, and better suited for the project's requirements.
