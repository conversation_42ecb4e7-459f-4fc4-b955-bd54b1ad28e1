# Cooldown System Documentation

## Overview

The cooldown system implements a 5-minute restriction between user bots and target bots to prevent spam and ensure fair distribution of requests. Each user bot can only send a link to a specific target bot once every 5 minutes.

## Key Features

- ✅ **5-minute cooldown**: Each user bot + target bot combination has a 5-minute cooldown period
- ✅ **0.5-second task checking**: System checks for new tasks every 0.5 seconds
- ✅ **Automatic bot rotation**: When a target bot is busy, the system automatically selects another available target bot
- ✅ **User bot fallback**: When all target bots are busy for one user bot, the system moves to the next user bot
- ✅ **Unique combinations**: Each user bot + target bot combination is tracked independently
- ✅ **Real-time monitoring**: Status monitoring shows cooldown information for all combinations

## Architecture

### Models

#### BotTargetCooldown
```python
class BotTargetCooldown(models.Model):
    user_bot_name = models.CharField(max_length=100)  # Name of user bot account
    target_bot = models.ForeignKey(TargetBots)        # Target bot that received the link
    last_sent_at = models.DateTime<PERSON>ield(auto_now=True) # When the last link was sent
    created_at = models.DateTime<PERSON>ield(auto_now_add=True)
```

### Services

#### CooldownService
Main service class that handles all cooldown logic:

- `get_available_target_bot(user_bot_name, hosting)` - Get available target bot for user bot
- `get_next_available_user_bot(user_bot_names, hosting)` - Find next available user bot + target bot combination
- `mark_bot_used(user_bot_name, target_bot)` - Mark combination as used (starts cooldown)
- `is_bot_available(user_bot_name, target_bot)` - Check if combination is available
- `get_cooldown_status(user_bot_name)` - Get detailed cooldown status for user bot

## How It Works

### 1. Task Processing Flow

```mermaid
graph TD
    A[New Task in Queue] --> B[Get Available User Bot + Target Bot Combination]
    B --> C{Combination Available?}
    C -->|Yes| D[Send Link to Target Bot]
    C -->|No| E[Wait 0.5 seconds]
    E --> B
    D --> F[Mark Combination as Used]
    F --> G[Start 5-minute Cooldown]
    G --> H[Process Response]
```

### 2. Bot Selection Logic

1. **Check Available Combinations**: System checks all user bot + target bot combinations for the requested hosting platform
2. **Filter by Cooldown**: Remove combinations that are in cooldown period
3. **Select First Available**: Choose the first available combination
4. **Fallback to Next User Bot**: If no combinations available for current user bot, try next user bot
5. **Wait if All Busy**: If all combinations are busy, wait 0.5 seconds and retry

### 3. Cooldown Management

- **Start Cooldown**: When a user bot sends a link to a target bot, a 5-minute cooldown starts
- **Track Independently**: Each user bot + target bot combination is tracked separately
- **Automatic Cleanup**: Old cooldown records are automatically cleaned up to prevent database bloat

## Configuration

### Constants (in `run_user_bots.py`)
```python
TASK_COOLDOWN_MINUTES = 5      # Cooldown period in minutes
QUEUE_CHECK_INTERVAL = 0.5     # Check interval in seconds
```

### Target Bot Capabilities
Target bots must be configured with platform capabilities:
```python
target_bot.can_dl_from_insta = True   # Can download from Instagram
target_bot.can_dl_from_tiktok = True  # Can download from TikTok  
target_bot.can_dl_from_youtube = True # Can download from YouTube
target_bot.is_active = True           # Bot is active and available
```

## Usage Examples

### Check Cooldown Status
```bash
# Check status for all user bots
python manage.py cooldown_status --action=status

# Check status for specific user bot
python manage.py cooldown_status --action=status --user-bot=account1

# Check status for specific target bot
python manage.py cooldown_status --action=status --target-bot=@YuklaydiBot
```

### Clear Cooldowns
```bash
# Clear all cooldowns (with confirmation)
python manage.py cooldown_status --action=clear

# Clear cooldowns for specific user bot
python manage.py cooldown_status --action=clear --user-bot=account1

# Clear cooldowns for specific target bot
python manage.py cooldown_status --action=clear --target-bot=@YuklaydiBot
```

### Test System
```bash
# Test cooldown logic
python manage.py cooldown_status --action=test
```

## Monitoring

### Real-time Status
The system provides real-time monitoring of:
- User bot status (busy/free)
- Cooldown status for each user bot + target bot combination
- Remaining cooldown time
- Available combinations

### Status Output Example
```
📊 ACCOUNT STATUS
==================================================
📱 account1: 🟢 FREE
📱 account2: 🔴 BUSY
   🔄 Current: https://instagram.com/p/example...

🕒 COOLDOWN STATUS (User Bot -> Target Bot)
============================================================

📱 account1:
      ✅ @YuklaydiBot: Available
      ⏳ @TargetBot2: 3m 45s remaining

📱 account2:
      ⏳ @YuklaydiBot: 1m 20s remaining
      ✅ @TargetBot2: Available
```

## Benefits

1. **Prevents Spam**: 5-minute cooldown prevents overwhelming target bots
2. **Fair Distribution**: Ensures requests are distributed across all available bots
3. **High Availability**: System continues working even when some bots are in cooldown
4. **Scalable**: Supports multiple user bots and target bots
5. **Efficient**: 0.5-second checking ensures quick response times
6. **Transparent**: Real-time monitoring shows system status

## Migration

The system includes database migrations to add the new `BotTargetCooldown` model. Run:

```bash
python manage.py migrate
```

## Backward Compatibility

The system maintains backward compatibility with existing code through legacy functions, but new implementations should use the `CooldownService` class for optimal performance.
