
from rest_framework import serializers
from bots.utils import TelegramBotUtils


class DownloadSerializer(serializers.Serializer):
    hosting = serializers.Char<PERSON>ield(max_length=255)
    url = serializers.URLField()
    bot_username = serializers.CharField(max_length=100, required=False)
    bot_token = serializers.CharField(max_length=255)
    chat_id = serializers.Char<PERSON>ield(max_length=100)

    def validate(self, data):
        """
        Validate and auto-fetch bot_username if not provided
        """
        bot_token = data.get('bot_token')
        bot_username = data.get('bot_username')

        if not bot_username and bot_token:
            try:
                fetched_username = TelegramBotUtils.get_bot_username(bot_token)
                if fetched_username:
                    data['bot_username'] = fetched_username
                else:
                    raise serializers.ValidationError(
                        "Could not retrieve bot username from token. Please provide bot_username manually."
                    )
            except Exception as e:
                raise serializers.ValidationError(
                    f"Invalid bot token or unable to fetch bot info: {str(e)}"
                )

        return data
