# Generated by Django 5.2.4 on 2025-07-23 04:45

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='DownloadTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('task_id', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed')], default='pending', max_length=20)),
                ('url', models.URLField()),
                ('hosting', models.CharField(default='instagram', max_length=50)),
                ('bot_token', models.CharField(max_length=255)),
                ('bot_username', models.CharField(blank=True, max_length=100, null=True)),
                ('chat_id', models.Char<PERSON>ield(max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('account_name', models.CharField(blank=True, max_length=100, null=True)),
                ('message_id', models.BigIntegerField(blank=True, null=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
