# Generated by Django 5.2.4 on 2025-07-24 13:36

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('bots', '0003_remove_is_queued_field'),
    ]

    operations = [
        migrations.CreateModel(
            name='BotTargetCooldown',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_bot_name', models.CharField(help_text='Name of the user bot account', max_length=100)),
                ('last_sent_at', models.DateTimeField(auto_now=True, help_text='When the last link was sent')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('target_bot', models.ForeignKey(help_text='Target bot that received the link', on_delete=django.db.models.deletion.CASCADE, to='bots.targetbots')),
            ],
            options={
                'indexes': [models.Index(fields=['user_bot_name', 'target_bot'], name='bots_bottar_user_bo_5b4a62_idx'), models.Index(fields=['last_sent_at'], name='bots_bottar_last_se_f1d201_idx')],
                'unique_together': {('user_bot_name', 'target_bot')},
            },
        ),
    ]
