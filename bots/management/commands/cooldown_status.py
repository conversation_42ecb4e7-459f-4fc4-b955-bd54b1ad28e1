"""
Management command to check and manage cooldown status between user bots and target bots.
"""

import asyncio
import glob
from django.core.management.base import BaseCommand
from bots.cooldown_service import CooldownService
from bots.models import TargetBots, BotTargetCooldown
from asgiref.sync import sync_to_async


class Command(BaseCommand):
    help = "Check and manage cooldown status between user bots and target bots"

    def add_arguments(self, parser):
        parser.add_argument(
            '--action',
            type=str,
            choices=['status', 'clear', 'test'],
            default='status',
            help='Action to perform: status (show cooldowns), clear (clear all cooldowns), test (test cooldown logic)'
        )
        parser.add_argument(
            '--user-bot',
            type=str,
            help='Specific user bot name to check/clear'
        )
        parser.add_argument(
            '--target-bot',
            type=str,
            help='Specific target bot username to check/clear'
        )

    def handle(self, *args, **options):
        action = options['action']
        
        if action == 'status':
            asyncio.run(self._show_status(options.get('user_bot'), options.get('target_bot')))
        elif action == 'clear':
            self._clear_cooldowns(options.get('user_bot'), options.get('target_bot'))
        elif action == 'test':
            asyncio.run(self._test_cooldown_logic())

    async def _show_status(self, user_bot_name=None, target_bot_username=None):
        """Show cooldown status"""
        self.stdout.write("\n" + "="*70)
        self.stdout.write("🕒 COOLDOWN STATUS")
        self.stdout.write("="*70)

        # Get all user bot accounts
        session_files = glob.glob("*.session")
        user_bot_names = [f.replace(".session", "") for f in session_files]

        if user_bot_name:
            user_bot_names = [user_bot_name] if user_bot_name in user_bot_names else []

        if not user_bot_names:
            self.stdout.write("❌ No user bot accounts found")
            return

        for account_name in user_bot_names:
            self.stdout.write(f"\n📱 {account_name}:")
            
            try:
                cooldown_status = await CooldownService.get_cooldown_status(account_name)
                
                if not cooldown_status:
                    self.stdout.write("      No target bots configured")
                    continue

                # Filter by target bot if specified
                if target_bot_username:
                    cooldown_status = {
                        k: v for k, v in cooldown_status.items() 
                        if k == target_bot_username or k == target_bot_username.replace('@', '')
                    }

                if not cooldown_status:
                    self.stdout.write(f"      Target bot '{target_bot_username}' not found")
                    continue
                    
                for target_bot, status_info in cooldown_status.items():
                    if status_info['available']:
                        self.stdout.write(f"      ✅ @{target_bot}: Available")
                    else:
                        remaining_min = status_info['remaining_seconds'] // 60
                        remaining_sec = status_info['remaining_seconds'] % 60
                        last_sent = status_info.get('last_sent_at', 'Unknown')
                        self.stdout.write(f"      ⏳ @{target_bot}: {remaining_min}m {remaining_sec}s remaining (last: {last_sent})")
                        
            except Exception as e:
                self.stdout.write(f"      ❌ Error getting cooldown status: {e}")

        # Show summary
        total_cooldowns = await sync_to_async(BotTargetCooldown.objects.count)()
        self.stdout.write(f"\n📊 Total cooldown records: {total_cooldowns}")
        self.stdout.write("="*70 + "\n")

    def _clear_cooldowns(self, user_bot_name=None, target_bot_username=None):
        """Clear cooldown records"""
        queryset = BotTargetCooldown.objects.all()

        if user_bot_name:
            queryset = queryset.filter(user_bot_name=user_bot_name)

        if target_bot_username:
            # Handle both @username and username formats
            clean_username = target_bot_username.replace('@', '')
            try:
                target_bot = TargetBots.objects.get(username=clean_username)
                queryset = queryset.filter(target_bot=target_bot)
            except TargetBots.DoesNotExist:
                self.stdout.write(f"❌ Target bot '{target_bot_username}' not found")
                return

        count = queryset.count()
        if count == 0:
            self.stdout.write("ℹ️ No cooldown records found to clear")
            return

        # Confirm deletion
        if user_bot_name and target_bot_username:
            message = f"Clear cooldown for {user_bot_name} -> {target_bot_username}?"
        elif user_bot_name:
            message = f"Clear all cooldowns for user bot '{user_bot_name}'?"
        elif target_bot_username:
            message = f"Clear all cooldowns for target bot '{target_bot_username}'?"
        else:
            message = f"Clear ALL {count} cooldown records?"

        confirm = input(f"{message} (y/N): ")
        if confirm.lower() == 'y':
            deleted_count = queryset.delete()[0]
            self.stdout.write(f"✅ Cleared {deleted_count} cooldown records")
        else:
            self.stdout.write("❌ Operation cancelled")

    async def _test_cooldown_logic(self):
        """Test cooldown logic with sample data"""
        self.stdout.write("\n" + "="*50)
        self.stdout.write("🧪 TESTING COOLDOWN LOGIC")
        self.stdout.write("="*50)

        # Get sample data
        session_files = glob.glob("*.session")
        if not session_files:
            self.stdout.write("❌ No user bot accounts found")
            return

        user_bot_name = session_files[0].replace(".session", "")
        target_bots = await CooldownService._get_available_target_bots(user_bot_name, "instagram")

        if not target_bots:
            self.stdout.write("❌ No target bots available for Instagram")
            return

        target_bot = target_bots[0]

        self.stdout.write(f"Testing with user bot: {user_bot_name}")
        self.stdout.write(f"Testing with target bot: @{target_bot.username}")

        # Test 1: Check initial availability
        is_available = await CooldownService.is_bot_available(user_bot_name, target_bot)
        self.stdout.write(f"\n1. Initial availability: {'✅ Available' if is_available else '❌ Not available'}")

        # Test 2: Mark as used
        await CooldownService.mark_bot_used(user_bot_name, target_bot)
        self.stdout.write("2. Marked as used")

        # Test 3: Check availability after marking as used
        is_available = await CooldownService.is_bot_available(user_bot_name, target_bot)
        self.stdout.write(f"3. Availability after use: {'✅ Available' if is_available else '❌ Not available (in cooldown)'}")

        # Test 4: Get cooldown status
        cooldown_status = await CooldownService.get_cooldown_status(user_bot_name)
        target_status = cooldown_status.get(target_bot.username, {})
        if not target_status.get('available', True):
            remaining = target_status.get('remaining_seconds', 0)
            self.stdout.write(f"4. Remaining cooldown: {remaining // 60}m {remaining % 60}s")

        # Test 5: Get available combinations
        combinations = await CooldownService.get_all_available_combinations([user_bot_name], "instagram")
        available_count = len(combinations)
        self.stdout.write(f"5. Available combinations for Instagram: {available_count}")

        self.stdout.write("\n✅ Test completed")
        self.stdout.write("="*50 + "\n")
