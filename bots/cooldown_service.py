"""
Cooldown management service for user bot and target bot interactions.
Handles the 5-minute cooldown logic and bot selection.
"""

import async<PERSON>
from typing import List, Optional, Tuple
from asgiref.sync import sync_to_async
from django.utils import timezone
from datetime import timed<PERSON><PERSON>

from .models import TargetBots, BotTargetCooldown
from .service import BotsService


class CooldownService:
    """
    Service to manage cooldown periods between user bots and target bots.
    Ensures each user bot can only send to a target bot once every 5 minutes.
    """
    
    COOLDOWN_MINUTES = 5
    
    @classmethod
    async def get_available_target_bot(cls, user_bot_name: str, hosting: str) -> Optional[TargetBots]:
        """
        Get an available target bot for the specified user bot and hosting platform.
        Returns None if no target bots are available (all in cooldown).
        """
        available_bots = await cls._get_available_target_bots(user_bot_name, hosting)
        
        if available_bots:
            # Return the first available bot (you could implement more sophisticated selection here)
            return available_bots[0]
        
        return None
    
    @classmethod
    async def get_next_available_user_bot(cls, user_bot_names: List[str], hosting: str) -> Optional[Tuple[str, TargetBots]]:
        """
        Find the next user bot that has available target bots for the specified hosting platform.
        Returns a tuple of (user_bot_name, target_bot) or None if no combinations are available.
        """
        for user_bot_name in user_bot_names:
            target_bot = await cls.get_available_target_bot(user_bot_name, hosting)
            if target_bot:
                return user_bot_name, target_bot
        
        return None
    
    @classmethod
    async def mark_bot_used(cls, user_bot_name: str, target_bot: TargetBots):
        """
        Mark that a user bot has sent a link to a target bot.
        This starts the 5-minute cooldown period.
        """
        await sync_to_async(BotTargetCooldown.mark_sent)(user_bot_name, target_bot)
    
    @classmethod
    async def is_bot_available(cls, user_bot_name: str, target_bot: TargetBots) -> bool:
        """
        Check if a user bot can send to a target bot (not in cooldown).
        """
        return await sync_to_async(BotTargetCooldown.is_available)(
            user_bot_name, target_bot, cls.COOLDOWN_MINUTES
        )
    
    @classmethod
    async def _get_available_target_bots(cls, user_bot_name: str, hosting: str) -> List[TargetBots]:
        """
        Get all target bots that are available for a specific user bot and hosting platform.
        """
        return await sync_to_async(BotTargetCooldown.get_available_target_bots)(
            user_bot_name, hosting, cls.COOLDOWN_MINUTES
        )
    
    @classmethod
    async def get_cooldown_status(cls, user_bot_name: str) -> dict:
        """
        Get the cooldown status for a user bot across all target bots.
        Returns a dictionary with target bot usernames and their cooldown status.
        """
        status = {}
        active_bots = await BotsService.get_active_bots()
        
        for target_bot in active_bots:
            is_available = await cls.is_bot_available(user_bot_name, target_bot)
            
            if not is_available:
                # Get the cooldown record to calculate remaining time
                try:
                    cooldown_record = await sync_to_async(BotTargetCooldown.objects.get)(
                        user_bot_name=user_bot_name, target_bot=target_bot
                    )
                    time_since_last = timezone.now() - cooldown_record.last_sent_at
                    remaining_time = timedelta(minutes=cls.COOLDOWN_MINUTES) - time_since_last
                    remaining_seconds = max(0, int(remaining_time.total_seconds()))
                    
                    status[target_bot.username] = {
                        'available': False,
                        'remaining_seconds': remaining_seconds,
                        'last_sent_at': cooldown_record.last_sent_at.isoformat()
                    }
                except BotTargetCooldown.DoesNotExist:
                    # This shouldn't happen if is_available returned False, but handle it
                    status[target_bot.username] = {
                        'available': True,
                        'remaining_seconds': 0,
                        'last_sent_at': None
                    }
            else:
                status[target_bot.username] = {
                    'available': True,
                    'remaining_seconds': 0,
                    'last_sent_at': None
                }
        
        return status
    
    @classmethod
    async def cleanup_old_cooldowns(cls, days_old: int = 7) -> int:
        """
        Clean up old cooldown records to prevent database bloat.
        Returns the number of records deleted.
        """
        return await sync_to_async(BotTargetCooldown.cleanup_old_records)(days_old)
    
    @classmethod
    async def get_all_available_combinations(cls, user_bot_names: List[str], hosting: str) -> List[Tuple[str, TargetBots]]:
        """
        Get all available user bot + target bot combinations for a hosting platform.
        Returns a list of tuples (user_bot_name, target_bot).
        """
        combinations = []
        
        for user_bot_name in user_bot_names:
            available_targets = await cls._get_available_target_bots(user_bot_name, hosting)
            for target_bot in available_targets:
                combinations.append((user_bot_name, target_bot))
        
        return combinations
    
    @classmethod
    async def wait_for_available_bot(cls, user_bot_names: List[str], hosting: str, 
                                   max_wait_seconds: int = 300) -> Optional[Tuple[str, TargetBots]]:
        """
        Wait for an available user bot + target bot combination.
        Checks every 0.5 seconds until a combination becomes available or timeout is reached.
        
        Args:
            user_bot_names: List of user bot names to check
            hosting: Hosting platform (instagram, tiktok, youtube)
            max_wait_seconds: Maximum time to wait in seconds (default: 5 minutes)
        
        Returns:
            Tuple of (user_bot_name, target_bot) or None if timeout reached
        """
        start_time = timezone.now()
        
        while True:
            # Check for available combinations
            combination = await cls.get_next_available_user_bot(user_bot_names, hosting)
            if combination:
                return combination
            
            # Check if we've exceeded the maximum wait time
            elapsed_time = (timezone.now() - start_time).total_seconds()
            if elapsed_time >= max_wait_seconds:
                return None
            
            # Wait 0.5 seconds before checking again
            await asyncio.sleep(0.5)
