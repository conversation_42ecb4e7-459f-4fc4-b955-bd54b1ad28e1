from django.db import models
from django.utils import timezone
from datetime import timedelta


class TargetBots(models.Model):
    """
    target bots
    """
    name = models.Char<PERSON>ield(max_length=100)
    username = models.Char<PERSON>ield(max_length=100)
    is_active = models.BooleanField(default=False)
    can_dl_from_insta = models.BooleanField(default=False)
    can_dl_from_tiktok = models.BooleanField(default=False)
    can_dl_from_youtube = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.name} - ({self.username})"
    
    @classmethod
    def get_bots(cls):
        return cls.objects.all()

    @classmethod
    def get_active_bots(cls):
        return cls.objects.filter(is_active=True)

    @classmethod
    def get_bot_by_username(cls, username: str):
        return cls.objects.get(username=username)


class BotTargetCooldown(models.Model):
    """
    Model to track cooldown periods between user bots and target bots.
    Each user bot can send to a target bot only once every 5 minutes.
    """
    user_bot_name = models.CharField(max_length=100, help_text="Name of the user bot account")
    target_bot = models.ForeignKey(TargetBots, on_delete=models.CASCADE, help_text="Target bot that received the link")
    last_sent_at = models.DateTimeField(auto_now=True, help_text="When the last link was sent")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user_bot_name', 'target_bot')
        indexes = [
            models.Index(fields=['user_bot_name', 'target_bot']),
            models.Index(fields=['last_sent_at']),
        ]

    def __str__(self):
        return f"{self.user_bot_name} -> {self.target_bot.username} (last: {self.last_sent_at})"

    @classmethod
    def is_available(cls, user_bot_name: str, target_bot: 'TargetBots', cooldown_minutes: int = 5) -> bool:
        """
        Check if a user bot can send to a target bot (not in cooldown)
        """
        try:
            cooldown_record = cls.objects.get(user_bot_name=user_bot_name, target_bot=target_bot)
            time_since_last = timezone.now() - cooldown_record.last_sent_at
            return time_since_last >= timedelta(minutes=cooldown_minutes)
        except cls.DoesNotExist:
            # No record means never sent before, so available
            return True

    @classmethod
    def mark_sent(cls, user_bot_name: str, target_bot: 'TargetBots'):
        """
        Mark that a user bot has sent a link to a target bot
        """
        cooldown_record, created = cls.objects.get_or_create(
            user_bot_name=user_bot_name,
            target_bot=target_bot
        )
        cooldown_record.last_sent_at = timezone.now()
        cooldown_record.save()
        return cooldown_record

    @classmethod
    def get_available_target_bots(cls, user_bot_name: str, hosting: str, cooldown_minutes: int = 5):
        """
        Get all target bots that are available for a specific user bot and hosting platform
        """
        # Get all active bots for the hosting platform
        suitable_bots = []
        active_bots = TargetBots.get_active_bots()

        for bot in active_bots:
            # Check if bot supports the hosting platform
            if hosting == 'instagram' and bot.can_dl_from_insta:
                suitable_bots.append(bot)
            elif hosting == 'tiktok' and bot.can_dl_from_tiktok:
                suitable_bots.append(bot)
            elif hosting == 'youtube' and bot.can_dl_from_youtube:
                suitable_bots.append(bot)

        # Filter out bots that are in cooldown
        available_bots = []
        for bot in suitable_bots:
            if cls.is_available(user_bot_name, bot, cooldown_minutes):
                available_bots.append(bot)

        return available_bots

    @classmethod
    def cleanup_old_records(cls, days_old: int = 7):
        """
        Clean up old cooldown records to prevent database bloat
        """
        cutoff_date = timezone.now() - timedelta(days=days_old)
        deleted_count = cls.objects.filter(last_sent_at__lt=cutoff_date).delete()[0]
        return deleted_count



