#!/usr/bin/env python
"""
Simple test to verify the cooldown system works.
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from bots.models import TargetBots, BotTargetCooldown

def test_basic_functionality():
    """Test basic model functionality"""
    print("🧪 Testing Basic Functionality")
    print("=" * 50)
    
    # Create a test target bot
    target_bot, created = TargetBots.objects.get_or_create(
        username="simple_test_bot",
        defaults={
            'name': 'Simple Test Bot',
            'is_active': True,
            'can_dl_from_insta': True,
            'can_dl_from_tiktok': True,
            'can_dl_from_youtube': True
        }
    )
    
    if created:
        print(f"✅ Created test target bot: @{target_bot.username}")
    else:
        print(f"ℹ️ Using existing target bot: @{target_bot.username}")
    
    user_bot_name = "simple_test_user"
    
    # Clear any existing cooldown
    BotTargetCooldown.objects.filter(
        user_bot_name=user_bot_name,
        target_bot=target_bot
    ).delete()
    
    # Test 1: Check initial availability
    print("\n1️⃣ Testing initial availability...")
    is_available = BotTargetCooldown.is_available(user_bot_name, target_bot)
    print(f"   Result: {'✅ Available' if is_available else '❌ Not available'}")
    assert is_available, "Bot should be available initially"
    
    # Test 2: Mark bot as used
    print("\n2️⃣ Marking bot as used...")
    cooldown_record = BotTargetCooldown.mark_sent(user_bot_name, target_bot)
    print(f"   ✅ Created cooldown record: {cooldown_record}")
    
    # Test 3: Check availability after use
    print("\n3️⃣ Checking availability after use...")
    is_available = BotTargetCooldown.is_available(user_bot_name, target_bot)
    print(f"   Result: {'✅ Available' if is_available else '❌ Not available (in cooldown)'}")
    assert not is_available, "Bot should be in cooldown after use"
    
    # Test 4: Get available target bots
    print("\n4️⃣ Getting available target bots for Instagram...")
    available_bots = BotTargetCooldown.get_available_target_bots(user_bot_name, "instagram")
    available_usernames = [bot.username for bot in available_bots]
    print(f"   Available bots: {available_usernames}")
    
    # Our test bot should not be in the available list
    assert target_bot.username not in available_usernames, "Test bot should not be available due to cooldown"
    
    # Test 5: Test with different user bot
    print("\n5️⃣ Testing with different user bot...")
    different_user = "different_test_user"
    is_available_different = BotTargetCooldown.is_available(different_user, target_bot)
    print(f"   Different user availability: {'✅ Available' if is_available_different else '❌ Not available'}")
    assert is_available_different, "Different user should have access to the target bot"
    
    # Test 6: Cleanup
    print("\n6️⃣ Testing cleanup...")
    deleted_count = BotTargetCooldown.cleanup_old_records(days_old=0)  # Delete all records
    print(f"   Cleaned up {deleted_count} old records")
    
    # Test 7: Verify cleanup worked
    print("\n7️⃣ Verifying cleanup...")
    is_available_after_cleanup = BotTargetCooldown.is_available(user_bot_name, target_bot)
    print(f"   Available after cleanup: {'✅ Yes' if is_available_after_cleanup else '❌ No'}")
    assert is_available_after_cleanup, "Bot should be available after cleanup"
    
    print("\n🎉 All basic tests passed!")
    
    # Cleanup test data
    if created:
        target_bot.delete()
        print("🧹 Cleaned up test target bot")
    
    print("=" * 50)


def test_model_methods():
    """Test model class methods"""
    print("\n🧪 Testing Model Methods")
    print("=" * 50)
    
    # Check if we have any active bots
    active_bots = TargetBots.get_active_bots()
    print(f"📊 Active target bots: {len(active_bots)}")
    
    for bot in active_bots:
        print(f"   - @{bot.username}: insta={bot.can_dl_from_insta}, tiktok={bot.can_dl_from_tiktok}, youtube={bot.can_dl_from_youtube}")
    
    # Check cooldown records
    cooldown_count = BotTargetCooldown.objects.count()
    print(f"📊 Total cooldown records: {cooldown_count}")
    
    print("=" * 50)


if __name__ == "__main__":
    try:
        test_basic_functionality()
        test_model_methods()
        print("\n✅ All tests completed successfully!")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
