#!/usr/bin/env python
"""
Demonstration script showing how the cooldown system works.
This script simulates the behavior without actually sending messages.
"""

import os
import sys
import django
import asyncio
from datetime import datetime, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from bots.models import TargetBots, BotTargetCooldown
from bots.cooldown_service import CooldownService
from asgiref.sync import sync_to_async


async def demo_cooldown_system():
    """Demonstrate the cooldown system functionality"""
    print("🎬 COOLDOWN SYSTEM DEMONSTRATION")
    print("=" * 60)
    
    # Get available user bots and target bots
    user_bots = ["13852724923_23072025", "17423784908"]  # Your actual user bots
    active_target_bots = await sync_to_async(list)(TargetBots.get_active_bots())
    
    print(f"👥 Available user bots: {len(user_bots)}")
    for bot in user_bots:
        print(f"   - {bot}")
    
    print(f"\n🎯 Available target bots: {len(active_target_bots)}")
    for bot in active_target_bots:
        platforms = []
        if bot.can_dl_from_insta: platforms.append("Instagram")
        if bot.can_dl_from_tiktok: platforms.append("TikTok")
        if bot.can_dl_from_youtube: platforms.append("YouTube")
        print(f"   - @{bot.username}: {', '.join(platforms)}")
    
    # Clear any existing cooldowns for demo
    await sync_to_async(BotTargetCooldown.objects.all().delete)()
    print(f"\n🧹 Cleared all existing cooldowns for demo")
    
    # Scenario 1: Show all available combinations
    print(f"\n📋 SCENARIO 1: Available Combinations")
    print("-" * 40)
    
    combinations = await CooldownService.get_all_available_combinations(user_bots, "instagram")
    print(f"Available Instagram combinations: {len(combinations)}")
    for i, (user_bot, target_bot) in enumerate(combinations[:5], 1):  # Show first 5
        print(f"   {i}. {user_bot} -> @{target_bot.username}")
    if len(combinations) > 5:
        print(f"   ... and {len(combinations) - 5} more")
    
    # Scenario 2: Use some combinations and show cooldown effect
    print(f"\n⏰ SCENARIO 2: Using Combinations (Cooldown Effect)")
    print("-" * 40)
    
    # Use first 2 combinations
    used_combinations = combinations[:2]
    for user_bot, target_bot in used_combinations:
        await CooldownService.mark_bot_used(user_bot, target_bot)
        print(f"✅ Used: {user_bot} -> @{target_bot.username}")
    
    # Show remaining combinations
    remaining_combinations = await CooldownService.get_all_available_combinations(user_bots, "instagram")
    print(f"\n📊 Remaining combinations: {len(remaining_combinations)} (was {len(combinations)})")
    print(f"   Cooldown effect: {len(combinations) - len(remaining_combinations)} combinations now in cooldown")
    
    # Scenario 3: Show cooldown status for each user bot
    print(f"\n🕒 SCENARIO 3: Detailed Cooldown Status")
    print("-" * 40)
    
    for user_bot in user_bots:
        print(f"\n📱 {user_bot}:")
        cooldown_status = await CooldownService.get_cooldown_status(user_bot)
        
        available_count = sum(1 for status in cooldown_status.values() if status['available'])
        cooldown_count = len(cooldown_status) - available_count
        
        print(f"   Available: {available_count}, In Cooldown: {cooldown_count}")
        
        # Show first few statuses
        for target_bot, status_info in list(cooldown_status.items())[:3]:
            if status_info['available']:
                print(f"   ✅ @{target_bot}: Available")
            else:
                remaining_min = status_info['remaining_seconds'] // 60
                remaining_sec = status_info['remaining_seconds'] % 60
                print(f"   ⏳ @{target_bot}: {remaining_min}m {remaining_sec}s remaining")
    
    # Scenario 4: Demonstrate bot selection logic
    print(f"\n🎯 SCENARIO 4: Smart Bot Selection")
    print("-" * 40)
    
    # Try to get next available combination
    next_combination = await CooldownService.get_next_available_user_bot(user_bots, "instagram")
    if next_combination:
        user_bot, target_bot = next_combination
        print(f"✅ Next available combination: {user_bot} -> @{target_bot.username}")
        print(f"   System automatically selected an available combination")
    else:
        print("❌ No available combinations (all in cooldown)")
    
    # Scenario 5: Show what happens when we use more combinations
    print(f"\n🔄 SCENARIO 5: Progressive Cooldown")
    print("-" * 40)
    
    # Use a few more combinations
    additional_combinations = remaining_combinations[:2]
    for user_bot, target_bot in additional_combinations:
        await CooldownService.mark_bot_used(user_bot, target_bot)
        print(f"✅ Used: {user_bot} -> @{target_bot.username}")
    
    # Show final status
    final_combinations = await CooldownService.get_all_available_combinations(user_bots, "instagram")
    total_used = len(combinations) - len(final_combinations)
    print(f"\n📊 Final Status:")
    print(f"   Total combinations used: {total_used}")
    print(f"   Remaining available: {len(final_combinations)}")
    print(f"   Cooldown efficiency: {(total_used / len(combinations) * 100):.1f}% of combinations utilized")
    
    # Scenario 6: Show cleanup functionality
    print(f"\n🧹 SCENARIO 6: Cleanup Demonstration")
    print("-" * 40)
    
    total_cooldowns = await sync_to_async(BotTargetCooldown.objects.count)()
    print(f"Current cooldown records: {total_cooldowns}")
    
    # Cleanup old records (in real scenario, this would be records older than 7 days)
    deleted_count = await CooldownService.cleanup_old_cooldowns(days_old=0)  # Delete all for demo
    print(f"Cleaned up: {deleted_count} records")
    
    # Verify cleanup
    remaining_cooldowns = await sync_to_async(BotTargetCooldown.objects.count)()
    print(f"Remaining cooldown records: {remaining_cooldowns}")
    
    # Show that all combinations are available again
    restored_combinations = await CooldownService.get_all_available_combinations(user_bots, "instagram")
    print(f"Available combinations after cleanup: {len(restored_combinations)}")
    
    print(f"\n🎉 DEMONSTRATION COMPLETE")
    print("=" * 60)
    print("\n💡 Key Takeaways:")
    print("   • Each user bot + target bot combination has independent 5-minute cooldown")
    print("   • System automatically finds available combinations")
    print("   • When target bots are busy, system rotates to available ones")
    print("   • When all target bots are busy for one user, system tries next user bot")
    print("   • Real-time monitoring shows exact cooldown status")
    print("   • Automatic cleanup prevents database bloat")


async def demo_real_time_selection():
    """Demonstrate real-time bot selection as it would work in production"""
    print(f"\n🚀 REAL-TIME SELECTION SIMULATION")
    print("=" * 60)
    
    user_bots = ["13852724923_23072025", "17423784908"]
    
    # Simulate processing 10 tasks
    for task_num in range(1, 11):
        print(f"\n📝 Processing Task #{task_num}")
        
        # Get available combination (this is what the queue processor does)
        combination = await CooldownService.get_next_available_user_bot(user_bots, "instagram")
        
        if combination:
            user_bot, target_bot = combination
            print(f"   ✅ Selected: {user_bot} -> @{target_bot.username}")
            
            # Mark as used (this is what happens after sending the message)
            await CooldownService.mark_bot_used(user_bot, target_bot)
            print(f"   🕒 Started 5-minute cooldown for this combination")
            
            # Show remaining available combinations
            remaining = await CooldownService.get_all_available_combinations(user_bots, "instagram")
            print(f"   📊 Remaining available combinations: {len(remaining)}")
            
        else:
            print(f"   ⏳ No available combinations - would wait 0.5 seconds and retry")
            break
        
        # Small delay to simulate processing time
        await asyncio.sleep(0.1)
    
    print(f"\n✅ Simulation complete - this shows how the system handles real tasks")


if __name__ == "__main__":
    try:
        asyncio.run(demo_cooldown_system())
        asyncio.run(demo_real_time_selection())
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
