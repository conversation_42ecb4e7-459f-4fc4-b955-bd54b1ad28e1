[Unit]
Description=SaverProxyBot Django Service (via Gunicorn)
Requires=saverapi.socket
After=network.target

[Service]
User=root
Group=root
WorkingDirectory=/root/apps/saverproxybot
Environment="PATH=/root/apps/saverproxybot/venv/bin"
EnvironmentFile=/root/apps/saverproxybot/.env
ExecStart=/root/apps/saverproxybot/venv/bin/gunicorn \
    --workers 3 \
    --bind unix:/run/saverapi.sock \
    backend.wsgi:application

Restart=always
RestartSec=5
TimeoutStopSec=20

[Install]
WantedBy=multi-user.target
